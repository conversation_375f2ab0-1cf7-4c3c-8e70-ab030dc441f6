<template>
  <div class="stage-container">
    <div class="stage-header">
      <div class="stage-title">{{ (stepIndex || 0) + 1 }}. 【句子测验】（1）听句子发音 （2）尝试自己翻译选择答案</div>
    </div>

    <div class="stage-content">
      <div class="word-display">
        <span class="word-display-text">{{ currentStepInfo?.wordInfo?.sentences.sentenceEn }}</span>
      </div>
      <div class="sentence-video-text">
        <span class="pronunciation uk-pronunciation" @click="playAudioUtil(currentStepInfo?.wordInfo?.sentences?.audioUkUrl)">英式<el-icon><VideoPlay /></el-icon></span>
        <span class="pronunciation us-pronunciation" @click="playAudioUtil(currentStepInfo?.wordInfo?.sentences?.audioUsUrl)" style="margin-left: 10px">美式<el-icon><VideoPlay /></el-icon></span>
      </div>
      <div class="translation-card">
        <!--        <div class="sentence-display">{{ currentSentence }}</div>-->

        <!-- 显示选项按钮（当选项隐藏时显示） -->
        <div v-if="!shouldShowOptions && !optionsVisible" class="show-options-button-container">
          <el-button type="primary" @click="showOptions" class="show-options-button">
            显示选项
          </el-button>
        </div>

        <!-- 选项容器 -->
        <div v-if="shouldShowOptions || optionsVisible" class="options-container">
          <div v-for="(option, index) in currentStepInfo?.step?.options" :key="index" class="option-item" :class="{
            'correct': showResult && index == correctAnswer,
            'wrong': showResult && selectedOption == index && index != correctAnswer
          }" @click="selectOption(index)">
            {{ getOptionLabel(index) }}: {{ option }}
            <el-icon v-if="showResult && index == correctAnswer" class="result-icon correct-icon"><Select /></el-icon>
            <el-icon v-if="showResult && selectedOption == index && index != correctAnswer"
                     class="result-icon wrong-icon">
              <Close />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 答对时的星星特效 -->
    <div v-if="showStars" class="stars-container">
      <div v-for="n in 10" :key="n" class="star" :style="getRandomStarStyle()"></div>
    </div>

    <!-- 答对时的奖励徽章 -->
    <div v-if="showReward" class="reward-badge">
      <el-icon class="reward-icon">
        <Trophy />
      </el-icon>
      <span class="reward-text">答对啦</span>
    </div>

  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, onUnmounted, computed} from 'vue'
import {Close, Select, Trophy, VideoPlay} from '@element-plus/icons-vue'
import {
  COURSE_sessionStorage_INFO_KEY,
  CurrentStepInfo,
  getStepInfoByType,
  getWordStepResultByWordId,
  submitCourseStepApi
} from "@/api/course";
import {getOptionIndex, getOptionLabel, getRandomStarStyle, playAudioUtil, useAudioPreloader} from '@/api/course/util'
import {ElMessageBox} from "element-plus";

const emit = defineEmits(['complete'])

const props = defineProps({
  selectWordText: String,
  courseId: {
    type: [String, Number],
    required: true
  },
  stepIndex: Number,
  sectionId: String
})
// 状态管理
const selectedOption = ref(-1)
const showResult = ref(false)
const showStars = ref(false)
const showReward = ref(false)
// 选项显示控制
const optionsVisible = ref(false)

// 获取当前课程设置
const getCurrentSettings = () => {
  try {
    // 从课程信息中获取studentId
    const courseInfoStr = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
    if (courseInfoStr) {
      const courseInfo = JSON.parse(courseInfoStr)
      const studentId = courseInfo?.student?.id
      if (studentId) {
        const settingsKey = `course_settings_${studentId}`
        const savedSettings = localStorage.getItem(settingsKey)
        if (savedSettings) {
          const settings = JSON.parse(savedSettings)
          return settings
        }
      }
    }
  } catch (error) {
    console.error('获取课程设置失败:', error)
  }
  return { showOptionsDefault: true } // 默认值
}

// 判断是否应该显示选项
const shouldShowOptions = computed(() => {
  // 如果用户已经点击了显示选项，则显示
  if (optionsVisible.value) {
    return true
  }

  // 移除sessionStorage检查，点击显示选项只对当前题目生效

  // 如果已经学习过（有提交结果），则始终显示选项
  if (showResult.value) {
    return true
  }

  // 检查当前单词是否已经学习过
  if (currentStepInfo.value?.step?.status === '已完成') {
    return true
  }

  // 获取当前设置
  const settings = getCurrentSettings()
  return settings.showOptionsDefault
})

const currentStepInfo = ref<CurrentStepInfo | null>(null);
const correctAnswer = ref(currentStepInfo?.step?.answer) // 当前单词的正确答案
const answerResult = ref(false)

// 音频预加载相关
let audioPreloader: ReturnType<typeof useAudioPreloader> | null = null;

const selectOption = (index: number) => {
  if (showResult.value) return // 如果已经显示结果，不允许再次选择
  selectedOption.value = index

  showResult.value = true

  if (index == correctAnswer.value) {
    answerResult.value = true
    showSuccessEffects()
  }
}

const showSuccessEffects = () => {
  showStars.value = true
  showReward.value = true
  setTimeout(() => {
    showStars.value = false
    showReward.value = false
  }, 2000)
}

// 显示选项
const showOptions = () => {
  optionsVisible.value = true
  // 不再持久化到sessionStorage，只对当前题目生效
}

/**
 * 提交课程步骤
 */
const submitCourseStep = ():boolean => {
  // 如果是已完成状态，不用重复提交
  let status = currentStepInfo.value.step.status
  if (status === "已完成") {
    return true;
  }
  if(selectedOption.value === -1) {
    // ElMessageBox.alert("还未选择答案哦，请先选择您的答案吧");
    return false;
  }
  let wordId = currentStepInfo.value.wordId
  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: answerResult.value ? "正确" : "错误",
    studentAnswer: getOptionLabel(selectedOption.value)
  }
  submitCourseStepApi(props.courseId, props.sectionId, wordId, submitResult, true)
  return true;
}


onMounted(async () => {
  // 移除sessionStorage检查，每个题目都重新开始

  // 先从sessionStorage获取
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    try {
      let courseInfo = JSON.parse(obj);
      currentStepInfo.value = await getStepInfoByType(courseInfo, '句子翻译', props.selectWordText)
      
      // 预加载音频文件
      if (currentStepInfo.value?.wordInfo?.sentences) {
        const audioUrls = [
          currentStepInfo.value.wordInfo.sentences.audioUkUrl,
          currentStepInfo.value.wordInfo.sentences.audioUsUrl
        ].filter(url => url && url.trim() !== '');
        
        if (audioUrls.length > 0) {
          console.log('开始预加载句子翻译音频文件:', audioUrls);
          audioPreloader = useAudioPreloader(audioUrls);
          await audioPreloader.preloadAudios();
          console.log('句子翻译音频预加载完成');
        }
      }
      
      if(currentStepInfo.value?.status == '待开始') {
        return ;
      }
      correctAnswer.value = currentStepInfo.value?.step?.answer
      // 获取当前阶段的提交结果
      let submitCourseStepApiParam = getWordStepResultByWordId(props.courseId, props.sectionId, currentStepInfo.value.wordId, courseInfo) || {}
      // 如果本地存在提交结果，直接渲染出结果
      submitCourseStepApiParam?.params?.find((item) => {
        if (item.stepId == currentStepInfo.value.step.id && (item.studentAnswer != null && item.studentAnswer != '自动结束')) {
          showResult.value = true
          selectedOption.value = getOptionIndex(item.studentAnswer)  // 学生的回答
        }
      })

      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }
})

// 不再需要在组件卸载时清除缓存，因为已在切换单词时清理
onUnmounted(() => {
  // 缓存清理已移至index.vue中的goToNextWord和changeCurrentStageType函数
})

defineExpose({
  submitCourseStep
})
</script>

<style scoped>
.stage-container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  overflow-y: hidden; /* 默认不显示滚动条，优先缩小内容 */
}

.stage-header {
  width: 100%;
  margin-bottom: 20px;
}

.stage-title {
  font-size: 24px;
  font-weight: bold;
  color: #5d4037;
}

.translation-card {
  width: 100%;
  padding: 0 20px 20px 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.word-display {
  font-size: clamp(18px, 3vw, 32px);
  color: #FF9800;
  font-weight: 600;
  text-align: center;
  max-width: 90%;
  margin: 0 auto;
  line-height: 1.4;
  font-family: "Comic Sans MS", cursive, sans-serif;
}

.sentence-display {
  font-size: clamp(16px, 2.5vw, 24px);
  color: #333;
  margin-bottom: 20px;
  text-align: center;
  line-height: 1.6;
}

.options-container {
  margin-top: 20px;
  width: 100%;
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 20px;
  color: #5d4037;
}



.option-item {
  padding: clamp(8px, 2vw, 16px);
  background-color: #fff;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: clamp(14px, 2vw, 24px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  border: 2px solid #f2ad47;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-item:hover:not(.correct, .wrong) {
  background-color: #c9e3ff;
  transform: translateY(-2px);
}

/* 正确答案样式 */
.option-item.correct {
  background-color: #c6f6d5;
  border-color: #48bb78;
  color: #276749;
}

/* 错误答案样式 */
.option-item.wrong {
  background-color: #fed7d7;
  border-color: #f56565;
  color: #c53030;
}

/* 结果图标样式 */
.result-icon {
  font-size: 20px;
}

.result-icon.correct-icon {
  color: #48bb78;
}

.result-icon.wrong-icon {
  color: #f56565;
}

.stage-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-height: 0;
}

.word-display {
  font-size: clamp(24px, 4vw, 40px);
  font-weight: bold;
  color: #5d4037;
  max-width: 90%;
  margin: 0 auto;
}

.word-display-text {
  font-family: "Comic Sans MS", cursive, sans-serif;
  margin-right: clamp(20px, 3vw, 40px);
  display: inline-block;
  line-height: 1.4;
  word-wrap: break-word;
}

/* 动画关键帧 */
@keyframes bounce {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  25% {
    transform: rotate(15deg);
  }

  75% {
    transform: rotate(-15deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

/* 星星特效样式 */
.stars-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

.star {
  position: absolute;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFD700"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>');
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0;
}

@keyframes starAnimation {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0;
  }
}

/* 奖励徽章样式 */
.reward-badge {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  background-color: #ffd700;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  z-index: 101;
  pointer-events: none;
}

.reward-icon {
  font-size: 32px;
  color: #ffffff;
  margin-bottom: 4px;
}

.reward-text {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

@keyframes badgeAnimation {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }

  70% {
    transform: translate(-50%, -50%) scale(1);
  }

  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

.sentence-video-text {
  font-size: 26px;
  font-weight: bold;
  color: #5d4037;
  margin: 10px 0 15px 0;
  display: inline-flex !important;
  align-items: center !important;
  cursor: pointer;
  font-family: "Comic Sans MS", cursive, sans-serif;
}

.pronunciation span {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pronunciation span:hover {
  color: #FF9800;
  transform: translateY(-2px);
}

.pronunciation .el-icon {
  margin-left: 8px;
  color: #FF9800;
  font-size: 26px;
  display: inline-flex;
  align-items: center;
}

/* 响应式设计 - 基于屏幕高度 */
@media screen and (max-height: 700px) {
  .stage-container {
    padding: 20px 15px;
  }

  .stage-title {
    font-size: 20px;
    margin-bottom: 20px;
  }

  .word-display {
    font-size: 28px;
  }

  .sentence-video-text {
    font-size: 18px;
    margin: 8px 0 12px 0;
  }
  .pronunciation .el-icon {
    font-size: 18px !important;
  }

  .options-container {
    gap: 12px;
  }

  .option-item {
    padding: 12px 15px;
    font-size: 16px;
  }

  .reward-badge {
    width: 70px;
    height: 70px;
  }

  .reward-icon {
    font-size: 28px;
  }

  .reward-text {
    font-size: 14px;
  }
}

@media screen and (max-height: 500px) {
  .stage-container {
    padding: 10px 8px;
  }

  .stage-title {
    font-size: 18px;
    margin-bottom: 10px;
  }

  .stage-header {
    margin-bottom: 15px;
  }

  .word-display {
    font-size: 24px;
  }

  .sentence-video-text {
    font-size: 16px;
    margin: 5px 0 10px 0;
  }
  .pronunciation .el-icon {
    font-size: 16px !important;
  }

  .options-container {
    gap: 8px;
  }

  .option-item {
    padding: 10px 12px;
    font-size: 14px;
  }

  .reward-badge {
    width: 60px;
    height: 60px;
  }

  .reward-icon {
    font-size: 24px;
  }

  .reward-text {
    font-size: 12px;
  }
}

/* 极小屏幕高度 - 进一步缩放避免滚动条 */
@media screen and (max-height: 450px) {
  .stage-container {
    padding: 8px 5px !important;
    font-size: 10px;
  }

  .stage-title {
    font-size: 15px !important;
    margin-bottom: 8px !important;
  }

  .stage-header {
    margin-bottom: 10px !important;
  }

  .word-display {
    font-size: 20px !important;
  }

  .sentence-video-text {
    font-size: 13px !important;
    margin: 5px 0 8px 0 !important;
  }
  .pronunciation .el-icon {
    font-size: 13px !important;
  }

  .options-container {
    gap: 6px !important;
  }

  .option-item {
    padding: 8px 10px !important;
    font-size: 12px !important;
  }

  .reward-badge {
    width: 50px !important;
    height: 50px !important;
  }

  .reward-icon {
    font-size: 20px !important;
    margin-bottom: 2px !important;
  }

  .reward-text {
    font-size: 10px !important;
  }
}

/* 超小屏幕高度 - 最大程度缩放 */
@media screen and (max-height: 380px) {
  .stage-container {
    padding: 5px 3px !important;
    font-size: 9px;
  }

  .stage-title {
    font-size: 13px !important;
    margin-bottom: 5px !important;
  }

  .stage-header {
    margin-bottom: 8px !important;
  }

  .word-display {
    font-size: 18px !important;
  }

  .sentence-video-text {
    font-size: 12px !important;
    margin: 3px 0 6px 0 !important;
  }
  .pronunciation .el-icon {
    font-size: 12px !important;
  }

  .options-container {
    gap: 4px !important;
  }

  .option-item {
    padding: 6px 8px !important;
    font-size: 11px !important;
  }

  .reward-badge {
    width: 45px !important;
    height: 45px !important;
  }

  .reward-icon {
    font-size: 18px !important;
    margin-bottom: 1px !important;
  }

  .reward-text {
    font-size: 9px !important;
  }
}

/* 极限小屏幕高度 - 最小可用尺寸 */
@media screen and (max-height: 320px) {
  .stage-container {
    padding: 3px 2px !important;
    font-size: 8px;
  }

  .stage-title {
    font-size: 11px !important;
    margin-bottom: 3px !important;
  }

  .stage-header {
    margin-bottom: 5px !important;
  }

  .word-display {
    font-size: 15px !important;
  }

  .sentence-video-text {
    font-size: 10px !important;
    margin: 2px 0 4px 0 !important;
  }
  .pronunciation .el-icon {
    font-size: 10px !important;
  }

  .options-container {
    gap: 3px !important;
  }

  .option-item {
    padding: 4px 6px !important;
    font-size: 9px !important;
  }

  .reward-badge {
    width: 35px !important;
    height: 35px !important;
  }

  .reward-icon {
    font-size: 14px !important;
    margin-bottom: 1px !important;
  }

  .reward-text {
    font-size: 7px !important;
  }
}

/* 1080p 150%缩放优化 - 基于屏幕高度 */
@media screen and (max-height: 720px) and (max-width: 1280px) {
  .stage-container {
    font-size: 13px;
    padding: 25px 15px;
  }

  .stage-title {
    font-size: 20px;
  }

  .word-display {
    font-size: 32px;
  }

  .sentence-video-text {
    font-size: 17px;
    margin: 8px 0 12px 0;
  }
  .pronunciation .el-icon {
    font-size: 17px !important;
  }

  .options-container {
    gap: 15px;
  }

  .option-item {
    padding: 12px 16px;
    font-size: 18px;
  }

  .reward-badge {
    width: 70px;
    height: 70px;
  }

  .reward-icon {
    font-size: 28px;
  }

  .reward-text {
    font-size: 14px;
  }
}

/* 动态字体缩放以避免滚动 */
@media (max-height: 800px) {
  .stage-container {
    font-size: 14px;
  }

  .stage-title {
    font-size: 22px !important;
  }

  .word-display {
    font-size: 36px !important;
  }

  .sentence-video-text {
    font-size: 19px !important;
  }
  .pronunciation .el-icon {
    font-size: 19px !important;
  }

  .option-item {
    padding: 14px 18px !important;
    font-size: 19px !important;
  }

  .reward-badge {
    width: 75px !important;
    height: 75px !important;
  }

  .reward-icon {
    font-size: 30px !important;
  }

  .reward-text {
    font-size: 15px !important;
  }
}

@media (max-height: 750px) {
  .stage-container {
    font-size: 13px;
    padding: 18px 12px;
  }

  .stage-title {
    font-size: 21px !important;
  }

  .word-display {
    font-size: 29px !important;
  }

  .sentence-video-text {
    font-size: 18px !important;
  }
  .pronunciation .el-icon {
    font-size: 18px !important;
  }

  .option-item {
    padding: 13px 16px !important;
    font-size: 16px !important;
  }

  .reward-badge {
    width: 72px !important;
    height: 72px !important;
  }

  .reward-icon {
    font-size: 29px !important;
  }

  .reward-text {
    font-size: 14px !important;
  }
}

@media (max-height: 600px) {
  .stage-container {
    font-size: 12px;
    padding: 15px 10px;
  }

  .stage-title {
    font-size: 14px !important;
  }

  .word-display {
    font-size: 26px !important;
  }

  .sentence-video-text {
    font-size: 16px !important;
  }
  .pronunciation .el-icon {
    font-size: 16px !important;
  }

  .option-item {
    padding: 11px 14px !important;
    font-size: 14px !important;
  }

  .reward-badge {
    width: 65px !important;
    height: 65px !important;
  }

  .reward-icon {
    font-size: 20px !important;
  }

  .reward-text {
    font-size: 13px !important;
  }
}

@media (max-height: 550px) {
  .stage-container {
    font-size: 11px;
    padding: 12px 8px;
  }

  .stage-title {
    font-size: 14px !important;
  }

  .word-display {
    font-size: 22px !important;
  }

  .sentence-video-text {
    font-size: 15px !important;
  }
  .pronunciation .el-icon {
    font-size: 15px !important;
  }

  .option-item {
    padding: 9px 12px !important;
    font-size: 13px !important;
  }

  .reward-badge {
    width: 55px !important;
    height: 55px !important;
  }

  .reward-icon {
    font-size: 22px !important;
  }

  .reward-text {
    font-size: 11px !important;
  }
}

.pronunciation:hover {
  color: #FF9800;
  transform: translateY(-2px);
}

/* 显示选项按钮样式 */
.show-options-button-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

:deep(.show-options-button) {
  width: 200px;
  height: 45px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

:deep(.show-options-button:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
</style>