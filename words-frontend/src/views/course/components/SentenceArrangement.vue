<template>
  <div class="stage-container">
    <div class="stage-header">
      <div class="stage-title">{{ (stepIndex || 0) + 1 }}. 【连词成句我会读】请把下列短语按正确顺序排列成完整句子</div>
    </div>

    <div class="stage-content">
      <div class="word-display">
        <span class="word-display-text">{{ currentStepInfo?.wordInfo?.sentences?.sentenceCn }}</span>
      </div>
      <div class="sentence-video-text">
        <span class="pronunciation uk-pronunciation" @click="playAudioUtil(currentStepInfo?.wordInfo?.sentences?.audioUkUrl)" style="margin-right: 20px">英式 <el-icon><VideoPlay /></el-icon></span>
        <span class="pronunciation uk-pronunciation" @click="playAudioUtil(currentStepInfo?.wordInfo?.sentences?.audioUsUrl)">美式 <el-icon><VideoPlay /></el-icon></span>
      </div>
      <div class="arrangement-card" >
<!--        <div class="chinese-sentence">{{ currentStepInfo?.wordInfo?.sentences?.sentenceCn }}</div>-->

        <!-- 题目区 -->
        <div class="question-zone">
          <div class="question-parts"
               :class="{ 'disabled': showResult }">
            <div v-for="(part, index) in availableParts"
                 :key="`question-part-${index}`"
                 class="question-part"
                 :class="{
                   'used': part.used
                 }"
                 @click="handlePartClick(part, index)">
              {{ part.text }}
            </div>
          </div>
        </div>

        <!-- 作答区 -->
        <div class="answer-zone">
          <div class="answer-slots"
               :class="{ 'disabled': showResult }">
            <div v-for="(slot, index) in answerSlots"
                 :key="`answer-slot-${index}`"
                 class="answer-slot"
                 :class="{
                   'filled': slot !== '',
                   'correct-slot': showResult && isCorrectOrder(),
                   'wrong-slot': showResult && !isCorrectOrder()
                 }"
                 @click="clearSlot(index)">
              <div v-if="slot !== ''" class="slot-content">
                {{ slot }}
              </div>
              <div v-else class="slot-placeholder">
                点击短语填入此处
              </div>
            </div>
          </div>
        </div>

        <div class="action-buttons">
          <el-button
            type="warning"
            size="mini"
            @click="resetOrder"
            :disabled="showResult">
            重置顺序
          </el-button>
          <el-button
            type="primary"
            size="mini"
            @click="checkAnswer"
            :disabled="showResult">
            检查答案
          </el-button>
        </div>

        <!-- 结果显示 -->
        <div v-if="showResult" class="result-display">
          <div v-if="isCorrectOrder()" class="correct-result">
            <el-icon class="result-icon"><Select /></el-icon>
            <span>答对了！句子排序正确</span>
          </div>
          <div v-else class="wrong-result">
            <el-icon class="result-icon"><Close /></el-icon>
            <span>答案不正确，正确顺序是：{{ getCorrectAnswerDisplay() }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 答对时的星星特效 -->
    <div v-if="showStars" class="stars-container">
      <div v-for="n in 10" :key="n" class="star" :style="getRandomStarStyle()"></div>
    </div>

    <!-- 答对时的奖励徽章 -->
    <div v-if="showReward" class="reward-badge">
      <el-icon class="reward-icon">
        <Trophy />
      </el-icon>
      <span class="reward-text">答对啦</span>
    </div>

  </div>
</template>

<script setup lang="ts">
import {onMounted, onUnmounted, ref} from 'vue'
import {Close, Select, Trophy, VideoPlay} from '@element-plus/icons-vue'
import {
  COURSE_sessionStorage_INFO_KEY,
  CurrentStepInfo,
  getStepInfoByType,
  getWordStepResultByWordId,
  submitCourseStepApi
} from "@/api/course";
import {getRandomStarStyle, playAudioUtil, useAudioPreloader} from '@/api/course/util'
import {ElMessageBox} from "element-plus";

const emit = defineEmits(['complete'])
const props = defineProps({
  selectWordText: String,
  courseId: {
    type: [String, Number],
    required: true
  },
  stepIndex: Number,
  sectionId: String
})

// 原有的响应式变量
const showResult = ref(false)
const showStars = ref(false)
const showReward = ref(false)
const currentStepInfo = ref<CurrentStepInfo | null>(null);
const correctAnswer = ref('')
const answerResult = ref(false)

// 音频预加载相关
let audioPreloader: ReturnType<typeof useAudioPreloader> | null = null;

// 新的拖拽相关响应式变量
interface QuestionPart {
  text: string;
  used: boolean;
}

const originalParts = ref<string[]>([])
const availableParts = ref<QuestionPart[]>([])
const answerSlots = ref<string[]>([])
const fillHistory = ref<Array<{slotIndex: number, partText: string, partIndex: number}>>([]) // 填入历史记录

// 初始化组件数据
const initializeParts = () => {
  if (currentStepInfo.value?.step?.sentenceOrder) {
    originalParts.value = [...currentStepInfo.value.step.sentenceOrder]
    
    // 初始化题目区短语
    availableParts.value = originalParts.value.map(text => ({
      text,
      used: false
    }))
    
    // 初始化作答区插槽
    answerSlots.value = new Array(originalParts.value.length).fill('')
  }
}

// 点击短语处理
const handlePartClick = (part: QuestionPart, index: number) => {
  if (showResult.value || part.used) return

  // 找到下一个空的插槽
  const nextEmptySlotIndex = answerSlots.value.findIndex(slot => slot === '')

  if (nextEmptySlotIndex !== -1) {
    // 记录填入历史
    fillHistory.value.push({
      slotIndex: nextEmptySlotIndex,
      partText: part.text,
      partIndex: index
    })

    // 填入短语
    answerSlots.value[nextEmptySlotIndex] = part.text
    part.used = true
  }
}

// 清空插槽
const clearSlot = (slotIndex: number) => {
  if (showResult.value || answerSlots.value[slotIndex] === '') return

  const partText = answerSlots.value[slotIndex]

  // 找到对应的短语并标记为未使用
  const partIndex = availableParts.value.findIndex(part => part.text === partText && part.used)
  if (partIndex !== -1) {
    availableParts.value[partIndex].used = false
  }

  // 清空插槽
  answerSlots.value[slotIndex] = ''

  // 从历史记录中移除相关记录
  const historyIndex = fillHistory.value.findIndex(h => h.slotIndex === slotIndex)
  if (historyIndex > -1) {
    fillHistory.value.splice(historyIndex, 1)
  }
}

// 将短语返回题目区
const returnToQuestionZone = (text: string) => {
  const partIndex = availableParts.value.findIndex(part => part.text === text)
  if (partIndex !== -1) {
    availableParts.value[partIndex].used = false
  }
}

// 重置顺序
const resetOrder = () => {
  if (showResult.value) return

  // 重置作答区
  answerSlots.value = new Array(originalParts.value.length).fill('')

  // 重置题目区
  availableParts.value.forEach(part => {
    part.used = false
  })

  // 清空历史记录
  fillHistory.value = []
}

// 检查答案是否正确
const isCorrectOrder = (): boolean => {
  const currentOrder = answerSlots.value.filter(slot => slot !== '').join(' ').trim()
  const currentAnser = (currentStepInfo.value?.wordInfo?.sentences?.sentenceEn || '').trim()
  
//   // 构建正确答案字符串
//   let expectedAnswer = ''
  
//   if (correctAnswer.value && currentStepInfo.value?.step?.sentenceOrder) {
//     // 检查答案是否为索引格式（如 "2, 3, 1"）
//     if (correctAnswer.value.includes(',')) {
//       // 解析索引并转换为实际短语序列
//       const indices = correctAnswer.value.split(',').map(i => parseInt(i.trim()))
//       const sentenceArray = currentStepInfo.value.step.sentenceOrder
      
//       const expectedParts = indices
//         .filter(index => index >= 0 && index < sentenceArray.length) // 过滤无效索引
//         .map(index => sentenceArray[index])
      
//       expectedAnswer = expectedParts.join(' ')
//     } else {
//       // 如果不是索引格式，直接使用原答案
//       expectedAnswer = correctAnswer.value
//     }
//   }
  
  // 可选：保留简单的验证日志（生产环境可删除）
  console.log('用户答案:', currentOrder)
  console.log('正确答案:', currentAnser)
  console.log('验证结果:', currentOrder === currentAnser)
  
  return currentOrder === currentAnser
}

// 获取正确答案的显示格式
const getCorrectAnswerDisplay = (): string => {
  return currentStepInfo.value?.wordInfo?.sentences?.sentenceEn || ''

  if (!correctAnswer.value || !currentStepInfo.value?.step?.sentenceOrder) {
    return correctAnswer.value
  }
  
  // 检查答案是否为索引格式（如 "2, 3, 1"）
  if (correctAnswer.value.includes(',')) {
    // 解析索引并转换为实际短语序列
    const indices = correctAnswer.value.split(',').map(i => parseInt(i.trim()))
    const sentenceArray = currentStepInfo.value.step.sentenceOrder
    
    const correctParts = indices
      .filter(index => index >= 0 && index < sentenceArray.length) // 过滤无效索引
      .map(index => sentenceArray[index])
    
    return correctParts.join(' ')
  } else {
    // 如果不是索引格式，直接使用原答案
    return correctAnswer.value
  }
}

// 检查答案
const checkAnswer = () => {
  if (showResult.value) return
  
  showResult.value = true
  answerResult.value = isCorrectOrder()
  
  if (answerResult.value) {
    showSuccessEffects()
    // 自动播放美音
    //
    playAudioUtil(currentStepInfo.value?.wordInfo?.sentences?.audioUsUrl)
  }
}

const showSuccessEffects = () => {
  showStars.value = true
  showReward.value = true
  setTimeout(() => {
    showStars.value = false
    showReward.value = false
  }, 2000)
}

/**
 * 提交课程步骤
 */
const submitCourseStep = ():number => {
  // 如果是已完成状态，不用重复提交
  let status = currentStepInfo.value?.step?.status
  if (status === "已完成") {
    return 1;
  }
  
  if (!showResult.value) {
    // ElMessageBox.alert("还未检查答案哦，请先检查您的答案吧");
    return -1;
  }
  
  let wordId = currentStepInfo.value?.wordId
  if (!wordId) return 0;

  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: answerResult.value ? "正确" : "错误",
    studentAnswer: answerSlots.value.filter(slot => slot !== '').join(' ')
  }
  submitCourseStepApi(props.courseId, props.sectionId, wordId, submitResult, true)
  return 1;
}

const splitStudentAnswerFlexible = (studentAnswer:string, sentenceOrder:any) => {
    let remaining = studentAnswer.trim();
    const result = [];
    const usedIndices = new Set(); // 记录已匹配的索引

    for (let i = 0; i < sentenceOrder.length; i++) {
        if (usedIndices.has(i)) continue; // 跳过已匹配的部分

        const part = sentenceOrder[i].trim();
        if (remaining.startsWith(part)) {
            result.push(remaining.slice(0, part.length));
            remaining = remaining.slice(part.length).trimStart();
            usedIndices.add(i);
            i = -1; // 重新从头开始检查（因为顺序可能乱序）
        }
    }

    // 如果还有剩余部分，添加到结果
    if (remaining) {
        result.push(remaining);
    }

    return result;
}

onMounted(async () => {
  // 先从sessionStorage获取
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    try {
      let courseInfo = JSON.parse(obj);
      currentStepInfo.value = await getStepInfoByType(courseInfo, '句子排序', props.selectWordText)
      correctAnswer.value = currentStepInfo.value?.step?.answer || ''
      
      // 预加载音频文件
      if (currentStepInfo.value?.wordInfo?.sentences) {
        const audioUrls = [
          currentStepInfo.value.wordInfo.sentences.audioUkUrl,
          currentStepInfo.value.wordInfo.sentences.audioUsUrl
        ].filter(url => url && url.trim() !== '');
        
        if (audioUrls.length > 0) {
          console.log('开始预加载句子排序音频文件:', audioUrls);
          audioPreloader = useAudioPreloader(audioUrls);
          await audioPreloader.preloadAudios();
          console.log('句子排序音频预加载完成');
        }
      }
      
      // 初始化短语数组
      initializeParts()

      if(currentStepInfo.value?.status == '待开始') {
        return ;
      }
      
      // 获取当前阶段的提交结果
      let submitCourseStepApiParam = getWordStepResultByWordId(props.courseId, props.sectionId, currentStepInfo.value.wordId, courseInfo) || {}
      // 如果本地存在提交结果，直接渲染出结果
      submitCourseStepApiParam?.params?.find((item) => {
        if (item.stepId == currentStepInfo.value?.step?.id && (item.studentAnswer != null && item.studentAnswer != '自动结束')) {
          showResult.value = true
          // 恢复学生的答案顺序
          const studentAnswer = item.studentAnswer
          if (studentAnswer) {
            // 这里不要用' '分割，用sentenceOrder来分割
            // const studentParts = studentAnswer.split(' ')
            const studentParts = splitStudentAnswerFlexible(studentAnswer, currentStepInfo.value?.step?.sentenceOrder)
            studentParts.forEach((part, index) => {
              if (index < answerSlots.value.length) {
                answerSlots.value[index] = part
                // 标记题目区对应短语为已使用
                const partIndex = availableParts.value.findIndex(p => p.text === part)
                if (partIndex !== -1) {
                  availableParts.value[partIndex].used = true
                }
              }
            })
            answerResult.value = isCorrectOrder()
          }
        }
      })
      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }
})

// 不再需要在组件卸载时清除缓存，因为已在切换单词时清理
onUnmounted(() => {
  // 缓存清理已移至index.vue中的goToNextWord和changeCurrentStageType函数
})

defineExpose({
  submitCourseStep
})
</script>

<style scoped>
.stage-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  /* 默认不显示滚动条，只在内容溢出时才显示 */
  overflow-y: auto;
}

.stage-header {
  width: 100%;
  margin-bottom: 20px;
}

.stage-title {
  font-size: 24px;
  font-weight: bold;
  color: #5d4037;
}

.arrangement-card {
  width: 100%;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stage-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-height: 0;
}

.word-display {
  margin-bottom: 10px;
  font-weight: bold;
  color: #5d4037;
}

.word-display-text {
  font-family: "Comic Sans MS", cursive, sans-serif;
  font-size: 40px;
}

.chinese-sentence {
  font-size: 22px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

.instructions {
  color: #FF9800;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
  font-size: 18px;
}

/* 作答区样式 */
.answer-zone {
  margin-bottom: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 2px solid #e9ecef;
}

.answer-zone-title {
  font-size: 18px;
  font-weight: bold;
  color: #495057;
  margin-bottom: 15px;
  text-align: center;
}

.answer-slots {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

.answer-slots.disabled {
  pointer-events: none;
  opacity: 0.7;
}

.answer-slot {
  width: 80%;
  height: 50px;
  border: 2px dashed #ced4da;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.answer-slot:hover {
  border-color: #409EFF;
  background: #ecf5ff;
  transform: scale(1.02);
}

.answer-slot.filled {
  border-color: #28a745;
  border-style: solid;
  background: #d4edda;
}

.answer-slot.filled:hover {
  border-color: #f44336;
  background: #ffebee;
  transform: scale(1.02);
}

.answer-slot.correct-slot {
  border-color: #48bb78;
  background: #c6f6d5;
}

.answer-slot.wrong-slot {
  border-color: #f56565;
  background: #fed7d7;
}

.slot-content {
  padding: 8px 12px;
  background: #fbd4a2;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: #5d4037;
  text-align: center;
  transition: all 0.3s ease;
  user-select: none;
}

.slot-content:hover {
  background: #f8d7b0;
  transform: scale(1.05);
}

.slot-placeholder {
  font-size: 14px;
  color: #adb5bd;
  text-align: center;
  padding: 0 5px;
}

/* 题目区样式 */
.question-zone {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 2px solid #e9ecef;
}



.question-zone-title {
  font-size: 18px;
  font-weight: bold;
  color: #495057;
  margin-bottom: 15px;
  text-align: center;
}

.question-parts {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.question-parts.disabled {
  pointer-events: none;
  opacity: 0.7;
}

.question-part {
  padding: 12px 18px;
  background: #fbd4a2;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 18px;
  font-weight: 500;
  color: #5d4037;
  text-align: center;
  user-select: none;
  position: relative;
  border: 2px solid transparent;
}

.question-part:active:not(.used) {
  transform: scale(0.95);
}

.question-part.used {
  opacity: 0.5;
  background: #e9ecef;
  cursor: not-allowed;
}

.question-part:not(.used):hover {
  background: #f8d7b0;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #FF9800;
}

.question-part:not(.used):hover::before {
  content: "点击我";
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  background: #5d4037;
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.question-part:not(.used):hover::before {
  opacity: 1;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
  margin-top: 20px; /* 添加顶部边距，确保与上方元素有足够间隔 */
}

.result-display {
  margin-top: 20px;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
}

.correct-result {
  background-color: #c6f6d5;
  color: #276749;
  border: 2px solid #48bb78;
}

.wrong-result {
  background-color: #fed7d7;
  color: #c53030;
  border: 2px solid #f56565;
}

.result-display .result-icon {
  font-size: 18px;
  margin-right: 8px;
  vertical-align: middle;
}

/* 移动端适配 - 屏幕宽度 */
@media (max-width: 768px) {
  .stage-container {
    padding: 20px 15px;
  }
  
  .stage-title {
    font-size: 20px;
  }
  
  .word-display-text {
    font-size: 32px;
  }
  
  .arrangement-card {
    padding: 15px;
  }
  
  .answer-slots {
    gap: 8px;
  }
  
  .answer-slot {
    width: 120px;
    height: 45px;
  }
  
  .question-zone {
    padding: 15px;
  }
  
  .question-parts {
    gap: 8px;
  }
  
  .question-part {
    padding: 10px 15px;
    font-size: 16px;
  }
  
  .slot-content {
    padding: 6px 10px;
    font-size: 14px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
  
  .sentence-video-text {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .stage-container {
    padding: 15px 10px;
  }
  
  .stage-title {
    font-size: 18px;
  }
  
  .word-display-text {
    font-size: 26px;
  }
  
  .arrangement-card {
    padding: 10px;
  }
  
  .answer-slot {
    width: 100px;
    height: 40px;
  }
  
  .question-zone {
    padding: 10px;
  }
  
  .question-part {
    padding: 8px 12px;
    font-size: 14px;
  }
  
  .slot-content {
    padding: 5px 8px;
    font-size: 12px;
  }
  
  .slot-placeholder {
    font-size: 12px;
  }
  
  .result-display {
    font-size: 14px;
    padding: 10px;
  }
  
  .sentence-video-text {
    font-size: 16px;
  }
  
  .reward-badge {
    width: 60px;
    height: 60px;
  }
  
  .reward-icon {
    font-size: 24px;
  }
  
  .reward-text {
    font-size: 12px;
  }
}

/* 响应式设计 - 屏幕高度 */
@media screen and (max-height: 700px) {
  .stage-container {
    padding: 15px 10px;
  }
  
  .stage-header {
    margin-bottom: 10px;
  }
  
  .stage-title {
    font-size: 18px;
  }
  
  .word-display-text {
    font-size: 30px;
  }
  
  .word-display {
    margin-bottom: 5px;
  }
  
  .sentence-video-text {
    margin: 5px 0;
    font-size: 16px;
  }
  
  .arrangement-card {
    padding: 15px;
    margin-top: 10px;
  }
  
  .question-zone {
    margin-bottom: 15px;
    padding: 15px;
  }
  
  .question-part {
    padding: 10px 15px;
    font-size: 16px;
  }
  
  .answer-zone {
    margin-bottom: 5px;
    padding: 8px;
  }
  
  .answer-slot {
    height: 45px;
  }
  
  .action-buttons {
    margin-top: 15px;
    margin-bottom: 15px;
    gap: 15px;
  }
}

@media screen and (max-height: 500px) {
  .stage-container {
    padding: 10px 8px;
  }
  
  .stage-header {
    margin-bottom: 5px;
  }
  
  .stage-title {
    font-size: 16px;
  }
  
  .word-display-text {
    font-size: 24px;
  }
  
  .word-display {
    margin-bottom: 3px;
  }
  
  .sentence-video-text {
    margin: 3px 0;
    font-size: 14px;
  }
  
  .arrangement-card {
    padding: 10px;
    margin-top: 5px;
  }
  
  .question-zone {
    margin-bottom: 10px;
    padding: 10px;
  }
  
  .question-part {
    padding: 8px 12px;
    font-size: 14px;
  }
  
  .answer-zone {
    margin-bottom: 3px;
    padding: 5px;
  }
  
  .answer-slot {
    height: 40px;
    width: 90px;
  }
  
  .slot-placeholder {
    font-size: 10px;
  }
  
  .action-buttons {
    margin-top: 10px;
    margin-bottom: 10px;
    gap: 10px;
  }
  
  .result-display {
    margin-top: 10px;
    padding: 8px;
    font-size: 12px;
  }
  
  .reward-badge {
    width: 50px;
    height: 50px;
  }
  
  .reward-icon {
    font-size: 20px;
  }
  
  .reward-text {
    font-size: 10px;
  }
}

/* 1080p 150%缩放优化 - 基于屏幕高度 */
@media screen and (max-height: 720px) and (max-width: 1280px) {
  .stage-container {
    font-size: 13px;
    padding: 15px 15px;
  }
  
  .stage-title {
    font-size: 14px !important;
  }
  
  .word-display-text {
    font-size: 28px;
  }
  
  .sentence-video-text {
    font-size: 16px !important;
  }
  .pronunciation .el-icon {
    font-size: 16px !important;
  }
  .arrangement-card {
    padding: 16px;
  }
  
  .question-zone {
    margin-bottom: 8px;
    padding: 8px;
  }
  
  .question-part {
    padding: 6px 8px;
    font-size: 13px;
  }
  
  .answer-zone {
    margin-bottom: 8px;
    padding: 8px;
  }
  
  .answer-slot {
    height: 40px;
  }
  
  .slot-content {
    font-size: 13px;
    padding: 6px 8px;
  }
  
  .slot-placeholder {
    font-size: 12px;
  }
  
  .action-buttons {
    margin-top: 10px;
    margin-bottom: 8px;
    gap: 16px;
  }
  
  .result-display {
    margin-top: 10px !important;
    font-size: 12px;
    padding: 2px 12px;
  }
  
  .reward-badge {
    width: 70px;
    height: 70px;
  }
  
  .reward-icon {
    font-size: 28px;
  }
  
  .reward-text {
    font-size: 14px;
  }
}

/* 极小屏幕高度 - 进一步缩放避免滚动条 */
@media screen and (max-height: 450px) {
  .stage-container {
    padding: 8px 5px !important;
    font-size: 10px;
  }
  
  .stage-title {
    font-size: 14px !important;
  }
  
  .word-display-text {
    font-size: 20px !important;
  }
  
  .word-display {
    margin-bottom: 3px !important;
  }
  
  .sentence-video-text {
    margin: 3px 0 !important;
    font-size: 12px !important;
  }
  
  .arrangement-card {
    padding: 8px !important;
    margin-top: 5px !important;
  }
  
  .question-zone {
    margin-bottom: 8px !important;
    padding: 8px !important;
  }
  
  .question-part {
    padding: 6px 10px !important;
    font-size: 12px !important;
  }
  
  .answer-zone {
    margin-bottom: 3px !important;
    padding: 4px !important;
  }
  
  .answer-slot {
    height: 35px !important;
    width: 80px !important;
  }
  
  .slot-placeholder {
    font-size: 9px !important;
  }
  
  .action-buttons {
    margin-top: 8px !important;
    margin-bottom: 8px !important;
    gap: 8px !important;
  }
}

/* 超小屏幕高度 - 最大程度缩放 */
@media screen and (max-height: 380px) {
  .stage-container {
    padding: 5px 3px !important;
    font-size: 9px;
  }
  
  .stage-title {
    font-size: 12px !important;
  }
  
  .word-display-text {
    font-size: 18px !important;
  }
  
  .sentence-video-text {
    font-size: 11px !important;
    margin: 2px 0 !important;
  }
  
  .arrangement-card {
    padding: 6px !important;
    margin-top: 3px !important;
  }
  
  .question-part {
    padding: 5px 8px !important;
    font-size: 11px !important;
  }
  
  .answer-slot {
    height: 30px !important;
    width: 70px !important;
  }
  
  .slot-placeholder {
    font-size: 8px !important;
  }
}

/* 极限小屏幕高度 - 最小可用尺寸 */
@media screen and (max-height: 320px) {
  .stage-container {
    padding: 3px 2px !important;
    font-size: 8px;
  }
  
  .stage-title {
    font-size: 10px !important;
  }
  
  .word-display-text {
    font-size: 15px !important;
  }
  
  .sentence-video-text {
    font-size: 9px !important;
    margin: 1px 0 !important;
  }
  
  .arrangement-card {
    padding: 4px !important;
    margin-top: 2px !important;
  }
  
  .question-part {
    padding: 3px 6px !important;
    font-size: 9px !important;
  }
  
  .answer-slot {
    height: 25px !important;
    width: 60px !important;
  }
  
  .slot-placeholder {
    font-size: 7px !important;
  }
  
  .action-buttons {
    margin-top: 5px !important;
    margin-bottom: 5px !important;
    gap: 5px !important;
  }
}

/* 动画关键帧 */
@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

/* 星星特效样式 */
.stars-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

.star {
  position: absolute;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFD700"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>');
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0;
}

@keyframes starAnimation {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0;
  }
}

/* 奖励徽章样式 */
.reward-badge {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  background-color: #ffd700;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  z-index: 101;
  pointer-events: none;
}

.reward-icon {
  font-size: 32px;
  color: #ffffff;
  margin-bottom: 4px;
}

.reward-text {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

@keyframes badgeAnimation {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }

  70% {
    transform: translate(-50%, -50%) scale(1);
  }

  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

.sentence-video-text {
  font-family: "Comic Sans MS", cursive, sans-serif;
  font-size: 26px;
  font-weight: bold;
  margin: 10px 0 10px 0;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
}

.pronunciation .el-icon {
  margin-left: 8px;
  color: #FF9800;
  font-size: 26px;
  display: inline-flex;
  align-items: center;
}
.pronunciation:hover {
  color: #FF9800;
  transform: translateY(-2px);
}
</style>