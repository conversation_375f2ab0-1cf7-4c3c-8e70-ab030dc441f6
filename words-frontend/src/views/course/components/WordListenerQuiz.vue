<template>
  <div class="word-test-container">
    <div class="test-header">
      <div class="test-title">{{ (stepIndex || 0) + 1 }}. 单词测验</div>
    </div>
    <div class="test-content">
      <div class="word-display">
        <span class="pronunciation uk-pronunciation"
              @click="playAudioUtil(currentStepInfo?.wordInfo?.audioUkUrl)">英式 {{ currentStepInfo?.wordInfo?.phoneticUk }}
          <el-icon>
            <VideoPlay />
          </el-icon></span>
        <span class="pronunciation us-pronunciation" @click="playAudioUtil(currentStepInfo?.wordInfo?.audioUsUrl)">美式 {{
            currentStepInfo?.wordInfo?.phoneticUs }} <el-icon>
            <VideoPlay />
          </el-icon></span>
      </div>
      <!-- 显示选项按钮（当选项隐藏时显示） -->
      <div v-if="!shouldShowOptions && !optionsVisible" class="show-options-button-container">
        <el-button type="primary" @click="showOptions" class="show-options-button">
          显示选项
        </el-button>
      </div>

      <!-- 选项容器 -->
      <div v-if="shouldShowOptions || optionsVisible" class="options-container">
        <div v-for="(option, index) in currentStepInfo?.step?.options" :key="index" class="option-item" :class="{
          'correct': showResult && index == correctAnswer,
          'wrong': showResult && selectedOption == index && index != correctAnswer
        }" @click="selectOption(index)" :disabled="showResult">
          {{ getOptionLabel(index) }}. {{ option }}
          <el-icon v-if="showResult && index == correctAnswer" class="result-icon correct-icon"><Select /></el-icon>
          <el-icon v-if="showResult && selectedOption == index && index != correctAnswer" class="result-icon wrong-icon">
            <Close />
          </el-icon>
        </div>
      </div>
      <div class="unknown-word-button" @click="markAsUnknown" :class="{ 'disabled': showResult, 'unknown-word-selected': unknownWord }">
        🤷‍♂️ 这个词我还不认识
      </div>
    </div>

    <!-- 答对时的星星特效 -->
    <div v-if="showStars" class="stars-container">
      <div v-for="n in 10" :key="n" class="star" :style="getRandomStarStyle()"></div>
    </div>

    <!-- 答对时的奖励徽章 -->
    <div v-if="showReward" class="reward-badge">
      <el-icon class="reward-icon">
        <Trophy />
      </el-icon>
      <span class="reward-text">答对啦</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import {defineProps, onMounted, onUnmounted, ref, computed} from 'vue'
import {Close, Select, Trophy, VideoPlay} from '@element-plus/icons-vue'
import {
  COURSE_sessionStorage_INFO_KEY,
  CurrentStepInfo,
  getStepInfoByType,
  getWordStepResultByWordId,
  submitCourseStepApi
} from "@/api/course";
import {getOptionIndex, getOptionLabel, getRandomStarStyle, playAudioUtil, useAudioPreloader} from '@/api/course/util'
import {ElMessage, ElMessageBox} from "element-plus";

const props = defineProps({
  selectWordText: String,
  courseId: {
    type: [String, Number],
    required: true
  },
  stepIndex: Number
})
const currentStepInfo = ref<CurrentStepInfo | null>(null);
// 基础数据
const correctAnswer = ref(currentStepInfo?.wordInfo?.step?.answer) // 当前单词的正确答案
// 状态管理
const selectedOption = ref(-1)
const showResult = ref(false)
const showStars = ref(false)
const showReward = ref(false)
const answerResult = ref(false)
const unknownWord = ref(false)
// 选项显示控制
const optionsVisible = ref(false)

// 获取当前课程设置
const getCurrentSettings = () => {
  try {
    // 从课程信息中获取studentId
    const courseInfoStr = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
    if (courseInfoStr) {
      const courseInfo = JSON.parse(courseInfoStr)
      const studentId = courseInfo?.student?.id
      if (studentId) {
        const settingsKey = `course_settings_${studentId}`
        const savedSettings = localStorage.getItem(settingsKey)
        if (savedSettings) {
          const settings = JSON.parse(savedSettings)
          return settings
        }
      }
    }
  } catch (error) {
    console.error('获取课程设置失败:', error)
  }
  return { showOptionsDefault: true } // 默认值
}

// 判断是否应该显示选项
const shouldShowOptions = computed(() => {
  // 如果用户已经点击了显示选项，则显示
  if (optionsVisible.value) {
    return true
  }

  // 如果已经学习过（有提交结果），则始终显示选项
  if (showResult.value) {
    return true
  }

  // 检查当前单词是否已经学习过
  if (currentStepInfo.value?.step?.status === '已完成') {
    return true
  }

  // 获取当前设置
  const settings = getCurrentSettings()
  return settings.showOptionsDefault
})

// 音频预加载相关
let audioPreloader: ReturnType<typeof useAudioPreloader> | null = null;

const showSuccessEffects = () => {
  showStars.value = true
  showReward.value = true
  setTimeout(() => {
    showStars.value = false
    showReward.value = false
  }, 2000)
}

// 显示选项
const showOptions = () => {
  optionsVisible.value = true
}

const selectOption = (option: number) => {
  if (showResult.value) return // 如果已经显示结果，不允许再次选择
  selectedOption.value = option
  showResult.value = true

  if (option == correctAnswer.value) {
    answerResult.value = true
    showSuccessEffects()
  }
}


/**
 * 提交课程步骤
 */
const markAsUnknown = () => {
  if (showResult.value) return // 如果已经显示结果，不允许再次操作
  
  showResult.value = true
  selectedOption.value = -2 // 使用-2表示"不认识"
  answerResult.value = false

  let wordId = currentStepInfo.value.wordId
  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: "错误",
    studentAnswer: "不会"
  }
  //debugger
  submitCourseStepApi(props.courseId, wordId, submitResult, true)
  // ElMessageBox.alert("已标记为不认识，下次再来吧");
  ElMessage.success('已标记为不认识，下次再来吧')
}

const submitCourseStep = ():boolean => {
  ////debugger
  // 如果是已完成状态，不用重复提交
  let status = currentStepInfo.value.step.status
  if (status === "已完成") {
    return true;
  }
  if(selectedOption.value === -1) {
    // ElMessageBox.alert("还未选择答案哦，请先选择您的答案吧");
    return false;
  }
  //debugger
  if(selectedOption.value === -2) { // 表示这个词不认识了
    return true;
  }
  let wordId = currentStepInfo.value.wordId

  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: answerResult.value ? "正确" : "错误",
    studentAnswer: getOptionLabel(selectedOption.value)
  }
  submitCourseStepApi(props.courseId, wordId, submitResult, true)
  return true;
}

onMounted(async () => {
  // 先从sessionStorage获取
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    try {
      let courseInfo = JSON.parse(obj);
      currentStepInfo.value = await getStepInfoByType(courseInfo, '单词测试2', props.selectWordText)
      
      // 预加载音频文件
      if (currentStepInfo.value?.wordInfo) {
        const audioUrls = [
          currentStepInfo.value.wordInfo.audioUkUrl,
          currentStepInfo.value.wordInfo.audioUsUrl
        ].filter(url => url && url.trim() !== '');
        
        if (audioUrls.length > 0) {
          console.log('开始预加载单词听力测试音频文件:', audioUrls);
          audioPreloader = useAudioPreloader(audioUrls);
          await audioPreloader.preloadAudios();
          console.log('单词听力测试音频预加载完成');
        }
      }
      
      correctAnswer.value = currentStepInfo.value?.step?.answer
      if(currentStepInfo.value?.status == '待开始') {
        return ;
      }
      // 获取当前阶段的提交结果
      let submitCourseStepApiParam = getWordStepResultByWordId(props.courseId, props.sectionId, currentStepInfo.value?.wordId, courseInfo) || {}
      // 如果本地存在提交结果，直接渲染出结果
      submitCourseStepApiParam?.params?.find((item) => {
        if (item.stepId == currentStepInfo.value?.step?.id && (item.studentAnswer != null && item.studentAnswer != '自动结束')) {
          showResult.value = true
          //debugger
          if(item.studentAnswer === '不会'){
            selectedOption.value = -2
            unknownWord.value = true
          } else {
            selectedOption.value = getOptionIndex(item.studentAnswer)  // 学生的回答
          }
        }
      })

      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }
})

// 不再需要在组件卸载时清除缓存，因为已在切换单词时清理
onUnmounted(() => {
  // 缓存清理已移至index.vue中的goToNextWord和changeCurrentStageType函数
})

defineExpose({
  submitCourseStep
})
</script>

<style scoped>
.word-test-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
}

.test-header {
  width: 100%;
  margin-bottom: 10px;
}

.test-title {
  font-size: 24px;
  font-weight: bold;
  color: #5d4037;
}

.test-content {
  width: 100%;
  max-width: 80%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.word-display {
  font-size: 70px;
  font-weight: bold;
  color: #5d4037;
  margin-bottom: 40px;
  font-family: "Comic Sans MS", cursive, sans-serif;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
}

.pronunciation {
  font-size: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.uk-pronunciation {
  margin-right: 20px;
}

.options-container {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  color: #5d4037;
}

.option-item {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 18px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  border: 2px solid #f2ad47;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-item:hover:not(.correct, .wrong) {
  background-color: #c9e3ff;
  transform: translateY(-2px);
}

/* 正确答案样式 */
.option-item.correct {
  background-color: #c6f6d5;
  border-color: #48bb78;
  color: #276749;
}

/* 错误答案样式 */
.option-item.wrong {
  background-color: #fed7d7;
  border-color: #f56565;
  color: #c53030;
}

/* 结果图标样式 */
.result-icon {
  font-size: 20px;
}

.result-icon.correct-icon {
  color: #48bb78;
}

.result-icon.wrong-icon {
  color: #f56565;
}

/* 动画关键帧 */
@keyframes bounce {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  25% {
    transform: rotate(15deg);
  }

  75% {
    transform: rotate(-15deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

/* 星星特效样式 */
.stars-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

.star {
  position: absolute;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFD700"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>');
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0;
}

@keyframes starAnimation {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0;
  }
}

/* 奖励徽章样式 */
.reward-badge {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  background-color: #ffd700;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  z-index: 101;
  pointer-events: none;
}

.reward-icon {
  font-size: 32px;
  color: #ffffff;
  margin-bottom: 4px;
}

.reward-text {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

@keyframes badgeAnimation {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }

  70% {
    transform: translate(-50%, -50%) scale(1);
  }

  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

/* 不认识按钮样式 */
.unknown-word-button {
  margin-top: 4vh;
  padding: 1.5vh 3vw;
  width: 80%;
  max-width: 400px;
  min-height: 48px;
  color: #5d4037;
  cursor: pointer;
  font-size: clamp(14px, 2vw, 16px);
  transition: all 0.3s ease;
  text-align: center;
  border: 2px dashed #f2ad47;
  border-radius: 8px;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unknown-word-button:hover:not(.disabled) {
  color: #f2ad47;
  background-color: rgba(242, 173, 71, 0.1);
}

.unknown-word-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #ccc;
  color: #999;
}
.unknown-word-selected {
  background-color: #fbb1b1  !important;
  color: #000000 !important;
  border: 2px solid red !important;
}

/* 响应式设计 - 针对不同屏幕高度 */
@media screen and (max-height: 700px) {
  .word-test-container {
    padding: 20px;
  }
  
  .test-title {
    font-size: 20px;
  }
  
  .word-display {
    font-size: 50px;
    margin-bottom: 20px;
    gap: 15px;
  }
  
  .pronunciation {
    font-size: 32px;
  }
  
  .uk-pronunciation {
    margin-right: 15px;
  }
  
  .options-container {
    gap: 15px;
  }
  
  .option-item {
    padding: 15px;
    font-size: 16px;
  }
  
  .unknown-word-button {
    margin-top: 10px;
    padding: 1vh 2vw;
    min-height: 40px;
    width: 90%;
    max-width: 380px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

@media screen and (max-height: 500px) {
  .word-test-container {
    padding: 15px 10px;
  }
  
  .test-title {
    font-size: 18px;
    margin-bottom: 5px;
  }
  
  .word-display {
    font-size: 40px;
    margin-bottom: 15px;
    gap: 10px;
    flex-direction: column;
    align-items: center;
  }
  
  .pronunciation {
    font-size: 24px;
  }
  
  .uk-pronunciation {
    margin-right: 0;
    margin-bottom: 5px;
  }
  
  .options-container {
    gap: 10px;
  }
  
  .option-item {
    padding: 10px;
    font-size: 14px;
  }
  
  .unknown-word-button {
    margin-top: 8px;
    padding: 0.8vh 2vw;
    min-height: 36px;
    font-size: 13px;
    width: 90%;
    max-width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 1080p 150%缩放优化 */
@media screen and (max-height: 720px) and (max-width: 1280px) {
  .word-test-container {
    padding: 22px;
    font-size: 13px;
  }
  
  .test-title {
    font-size: 21px !important;
  }
  
  .word-display {
    font-size: 52px !important;
    margin-bottom: 22px;
    gap: 16px;
  }
  
  .pronunciation {
    font-size: 34px !important;
  }
  
  .uk-pronunciation {
    margin-right: 16px;
  }
  
  .options-container {
    gap: 16px;
  }
  
  .option-item {
    padding: 16px;
    font-size: 17px;
  }
  
  .unknown-word-button {
    margin-top: 12px;
    padding: 1.2vh 2.2vw;
    min-height: 42px;
    width: 90%;
    max-width: 400px;
  }
}

@media screen and (max-height: 450px) {
  .word-test-container {
    padding: 8px 5px !important;
    font-size: 10px;
  }
  
  .test-title {
    font-size: 16px !important;
    margin-bottom: 5px !important;
  }
  
  .word-display {
    font-size: 30px !important;
    margin-bottom: 12px !important;
    gap: 8px !important;
    flex-direction: column;
    align-items: center;
  }
  
  .pronunciation {
    font-size: 20px !important;
  }
  
  .uk-pronunciation {
    margin-right: 0 !important;
    margin-bottom: 4px !important;
  }
  
  .options-container {
    gap: 8px !important;
  }
  
  .option-item {
    padding: 8px !important;
    font-size: 12px !important;
  }
}

/* 超小屏幕高度 - 最大程度缩放 */
@media screen and (max-height: 380px) {
  .word-test-container {
    padding: 5px 3px !important;
    font-size: 9px;
  }
  
  .test-title {
    font-size: 14px !important;
    margin-bottom: 3px !important;
  }
  
  .word-display {
    font-size: 26px !important;
    margin-bottom: 8px !important;
    gap: 6px !important;
  }
  
  .pronunciation {
    font-size: 18px !important;
  }
  
  .option-item {
    padding: 6px !important;
    font-size: 11px !important;
  }
}

/* 极限小屏幕高度 - 最小可用尺寸 */
@media screen and (max-height: 320px) {
  .word-test-container {
    padding: 3px 2px !important;
    font-size: 8px;
  }
  
  .test-title {
    font-size: 12px !important;
    margin-bottom: 2px !important;
  }
  
  .word-display {
    font-size: 22px !important;
    margin-bottom: 6px !important;
    gap: 4px !important;
  }
  
  .pronunciation {
    font-size: 15px !important;
  }
  
  .option-item {
    padding: 4px !important;
    font-size: 9px !important;
  }
  
  .unknown-word-button {
    margin-top: 5px;
    padding: 0.5vh 1.5vw;
    min-height: 30px;
    font-size: 12px;
    width: 90%;
    max-width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 显示选项按钮样式 */
.show-options-button-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

:deep(.show-options-button) {
  width: 200px;
  height: 45px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

:deep(.show-options-button:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
</style>