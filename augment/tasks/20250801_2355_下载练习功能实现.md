# 下载练习功能实现任务记录

## 任务概述
为教师教材管理页面增加下载练习功能，交互与下载讲义保持一致，生成练习PDF文件，生成逻辑同课堂练习，只需要一份。

## 实现内容

### 1. 前端实现

#### 1.1 API接口定义
- 在 `words-frontend/src/api/textbook/index.ts` 中添加：
  - `TextbookDownloadExerciseReq` 接口定义
  - `TextbookDownloadExerciseResp` 接口定义  
  - `downloadTextbookExercise` API函数

#### 1.2 页面功能
- 在 `words-frontend/src/views/teacher/material-manager/index.vue` 中添加：
  - 下载练习按钮（在下载音频按钮后面）
  - 下载练习对话框（与下载讲义对话框样式一致）
  - 下载练习相关的状态变量和处理函数
  - 支持整本下载和自选范围下载

#### 1.3 交互逻辑
- 点击"下载练习"按钮打开对话框
- 支持选择下载方式：整本下载、自选范围
- 自选范围时可以设置开始和结束单词序号
- 下载完成后自动触发浏览器下载PDF文件

### 2. 后端实现

#### 2.1 Controller层
- 在 `TextbookController` 中添加 `downloadExercise` 接口
- 接收下载请求参数，调用服务层生成PDF
- 返回Base64编码的PDF数据和响应头

#### 2.2 Service层
- 在 `TextbookManagerService` 接口中添加 `generateExercisePdf` 方法
- 在 `TextbookManagerServiceImpl` 中实现练习PDF生成逻辑
- 复用现有的课堂练习生成逻辑

#### 2.3 核心逻辑
- 根据教材项ID列表构建课程环节数据
- 调用 `CourseService.generatePracticesInfoPdf` 生成练习PDF
- 使用与课堂练习相同的练习要求和格式

### 3. 技术细节

#### 3.1 PDF生成
- 复用现有的 `generatePracticesInfoPdf` 方法
- 练习要求1：看音标，拼读一遍，写出单词...
- 练习要求2：看句子翻译，对照讲义抄写1遍英文句子...
- PDF格式与课堂练习保持一致

#### 3.2 数据流转
1. 前端发送下载请求（教材ID、下载类型、范围等）
2. 后端根据请求参数获取目标单词项ID列表
3. 构建课程环节数据结构
4. 生成练习PDF字节流
5. 转换为Base64返回给前端
6. 前端解码并触发下载

## 文件修改清单

### 前端文件
- `words-frontend/src/api/textbook/index.ts` - 添加API接口
- `words-frontend/src/views/teacher/material-manager/index.vue` - 添加UI和逻辑

### 后端文件
- `words-service/wss-rest/src/main/java/org/nonamespace/word/rest/controller/TextbookController.java` - 添加下载接口
- `words-service/wss-server/src/main/java/org/nonamespace/word/server/facade/TextbookManagerService.java` - 添加接口定义
- `words-service/wss-server/src/main/java/org/nonamespace/word/server/facade/impl/TextbookManagerServiceImpl.java` - 实现练习生成
- `words-service/wss-server/src/main/java/org/nonamespace/word/server/service/ICourseService.java` - 添加公开方法
- `words-service/wss-server/src/main/java/org/nonamespace/word/server/service/impl/CourseServiceImpl.java` - 修改方法可见性

## 功能特点

1. **交互一致性**：与下载讲义功能保持完全一致的用户体验
2. **复用现有逻辑**：充分利用现有的课堂练习生成代码，避免重复开发
3. **灵活下载**：支持整本下载和自选范围下载
4. **格式统一**：生成的练习PDF与课堂练习格式完全一致
5. **错误处理**：完善的参数验证和错误提示

## 测试建议

1. 测试整本下载功能
2. 测试自选范围下载功能
3. 测试边界条件（如范围超出教材单词数）
4. 测试生成的PDF格式和内容正确性
5. 测试下载流程的完整性

## 完成状态
✅ 前端API接口定义
✅ 前端UI组件和交互逻辑
✅ 后端Controller接口
✅ 后端Service层实现
✅ PDF生成逻辑复用
✅ 代码编译检查
