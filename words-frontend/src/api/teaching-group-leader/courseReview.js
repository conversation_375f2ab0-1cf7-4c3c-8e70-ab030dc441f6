import request from '@/utils/request'

// 获取待审核的预约课申请列表
export function getPendingApplicationsApi(params) {
  return request({
    url: '/teaching-group-leader/pending-applications',
    method: 'get',
    params
  })
}

// 获取预约课申请列表（支持多状态查询）
export function getCourseBookingReviewListApi(params) {
  return request({
    url: '/course-booking-review/list',
    method: 'get',
    params
  })
}

// 获取审核统计信息
export function getReviewStatsApi() {
  return request({
    url: '/teaching-group-leader/review-stats',
    method: 'get'
  })
}

// 审核预约课申请
export function reviewApplicationApi(data) {
  return request({
    url: '/teaching-group-leader/review-application',
    method: 'post',
    data
  })
}

// 导出预约课申请列表（仅admin和hr可用）
export function exportCourseBookingApplicationsApi(data) {
  return request({
    url: '/course-booking-review/export',
    method: 'post',
    data
  })
}

// 批量审核预约课申请
export function batchReviewApplicationsApi(data) {
  return request({
    url: '/teaching-group-leader/batch-review',
    method: 'post',
    data
  })
}

// 获取教学组下的教师列表
export function getGroupTeachersApi(params) {
  return request({
    url: '/teaching-group-leader/group-teachers',
    method: 'get',
    params
  })
}

// 获取指定申请中本组内的候选教师列表（用于通过申请页面）
export function getAppliedTeachersApi(applicationId, params) {
  return request({
    url: '/teaching-group-leader/applied-teachers',
    method: 'get',
    params: {
      applicationId,
      ...params
    }
  })
}

// 获取教师可用时间段
export function getTeacherAvailableSlotsApi(teacherId, applicationId) {
  return request({
    url: '/teaching-group-leader/teacher-available-slots',
    method: 'get',
    params: {
      teacherId,
      applicationId
    }
  })
}

// 分配教师和时间段
export function assignTeacherApi(data) {
  return request({
    url: '/teaching-group-leader/assign-teacher',
    method: 'post',
    data
  })
}

// 获取审核历史记录
export function getReviewHistoryApi(params) {
  return request({
    url: '/teaching-group-leader/review-history',
    method: 'get',
    params
  })
}

// 导出审核报告
export function exportReviewReportApi(data) {
  return request({
    url: '/teaching-group-leader/export-review-report',
    method: 'post',
    data
  })
}

// 获取当前用户的教学组信息
export function getMyTeachingGroupApi() {
  return request({
    url: '/teaching-group-leader/my-teaching-group',
    method: 'get'
  })
}

// 设置审核规则
export function setReviewRulesApi(data) {
  return request({
    url: '/teaching-group-leader/set-review-rules',
    method: 'post',
    data
  })
}

// 获取审核规则
export function getReviewRulesApi() {
  return request({
    url: '/teaching-group-leader/review-rules',
    method: 'get'
  })
}

// 获取预约课申请详情（教学组长专用）
export function getCourseBookingReviewDetailApi(id) {
  return request({
    url: `/course-booking-review/${id}`,
    method: 'get'
  })
}
