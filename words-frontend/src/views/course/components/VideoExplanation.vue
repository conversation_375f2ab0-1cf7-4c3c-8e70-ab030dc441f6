<template>
  <div class="stage-container">
    <div class="stage-header">
      <div class="stage-title">{{ (stepIndex || 0) + 1 }}. 欢迎来到筱灵老师的超级单词讲解！<br>请先认真听课记笔记，然后和你的老师一起搞定后面的挑战噢！</div>
    </div>

    <div class="stage-content">
      <!--      <div class="word-display">-->
      <!--        <span class="word-display-text">{{ currentStepInfo?.wordInfo?.word }}</span>-->
      <!--      </div>-->
      <div class="video-card">
        <div ref="videoContainer" class="video-container" v-if="currentStepInfo?.wordInfo?.videoUrl"></div>
        <div class="video-wrapper" v-else>
          <div class="video-placeholder">
            <el-icon :size="50">
              <Close />
            </el-icon>
            <p style="text-align: center">暂无视频</p>
          </div>
        </div>

        <!--        <div class="video-info">-->
        <!--          <h4 >单词"<b class="video-title">{{ currentStepInfo?.wordInfo?.word }}</b>"的用法讲解</h4>-->
        <!--          <div class="video-description">本视频详细讲解单词"<b>{{ currentStepInfo?.wordInfo?.word }}</b>"的各种用法、搭配和常见错误。</div>-->
        <!--        </div>-->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, watch, onMounted, onBeforeUnmount} from 'vue'
import {Close} from '@element-plus/icons-vue'
import {COURSE_sessionStorage_INFO_KEY, CurrentStepInfo, getStepInfoByType, submitCourseStepApi} from "@/api/course";
import {preloadVideoUtil, useVideoPreloader} from "@/api/course/util";

const props = defineProps({
  selectWordText: String,
  courseId: {
    type: [String, Number],
    required: true
  },
  stepIndex: Number,
  sectionId: String
})

const emit = defineEmits(['complete'])

const currentStepInfo = ref<CurrentStepInfo | null>(null);
const videoContainer = ref<HTMLDivElement | null>(null);
const currentVideoElement = ref<HTMLVideoElement | null>(null);

/**
 * 提交课程步骤
 */
const submitCourseStep = () => {
  // 如果是已完成状态，不用重复提交
  let status = currentStepInfo.value.step.status
  if (status === "已完成") {
    return;
  }
  let wordId = currentStepInfo.value.wordId

  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: "正确",
    studentAnswer: "正确"
  }
  submitCourseStepApi(props.courseId, props.sectionId, wordId, submitResult, true)
}

/**
 * 加载视频并显示
 * @param url 视频URL
 */
const loadAndDisplayVideo = (url: string) => {
  // 移除旧的视频元素
  if (currentVideoElement.value && videoContainer.value?.contains(currentVideoElement.value)) {
    try {
      videoContainer.value.removeChild(currentVideoElement.value);
    } catch (e) {
      console.error('移除视频元素失败', e);
    }
    currentVideoElement.value = null;
  }
  
  // 如果没有URL或容器，直接返回
  if (!url || url.trim() === '' || !videoContainer.value) {
    console.warn('无效的视频URL或容器不存在');
    return;
  }
  
  try {
    // 从缓存获取或创建新的视频元素
    const video = preloadVideoUtil(url);
    if (!video) {
      console.warn(`无法加载视频: ${url}`);
      return;
    }
    
    // 设置视频属性
    video.controls = true;
    video.style.maxWidth = '40%';
    video.style.maxHeight = '70vh';
    video.muted = false; // 取消静音，允许播放声音
    
    // 添加到容器中
    videoContainer.value.appendChild(video);
    currentVideoElement.value = video;
  } catch (error) {
    console.error(`加载视频时发生错误: ${url}`, error);
  }
}

// 监听视频URL变化
watch(() => currentStepInfo.value?.wordInfo?.videoUrl, (newUrl) => {
  // 确保URL有效再加载视频
  if (newUrl && typeof newUrl === 'string' && newUrl.trim() !== '') {
    console.log(`加载视频: ${newUrl}`);
    loadAndDisplayVideo(newUrl);
  } else {
    console.warn('检测到无效的视频URL，跳过加载');
  }
}, { immediate: true });

onMounted(async () => {
  // 先从sessionStorage获取
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    try {
      let courseInfo = JSON.parse(obj);
      currentStepInfo.value = await getStepInfoByType(courseInfo, '视频讲解', props.selectWordText)
      
      // 加载视频
      if (currentStepInfo.value?.wordInfo?.videoUrl) {
        loadAndDisplayVideo(currentStepInfo.value.wordInfo.videoUrl);
      }
      
      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }
})

// 组件卸载前移除视频元素，但不销毁它（保留在缓存中）
onBeforeUnmount(() => {
  if (currentVideoElement.value && videoContainer.value?.contains(currentVideoElement.value)) {
    try {
      videoContainer.value.removeChild(currentVideoElement.value);
    } catch (e) {
      console.error('移除视频元素失败', e);
    }
    currentVideoElement.value = null;
  }
})

/**
 * 停止当前视频播放
 */
const stopCurrentVideo = () => {
  if (currentVideoElement.value) {
    currentVideoElement.value.pause();
    currentVideoElement.value.currentTime = 0;
  }
};

defineExpose({
  submitCourseStep,
  stopCurrentVideo
})
</script>

<style scoped>
.stage-container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  padding: 40px;
}

.stage-header {
  width: 100%;
  margin-bottom: 20px;
}

.stage-title {
  font-size: 24px;
  font-weight: bold;
  color: #5d4037;
}

.video-card {
  width: 100%;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.video-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.stage-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-height: 0;
}

.video-wrapper {
  width: 40%;
  position: relative;
  /* 16:9 比例 */
  padding-bottom: 22.5%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  cursor: pointer;
}

.video-placeholder .el-icon {
  color: #FF9800;
  margin-bottom: 10px;
}

/* 1080p 150%缩放优化 */
@media screen and (max-height: 720px) and (max-width: 1280px) {
  .stage-container {
    padding: 32px;
    font-size: 13px;
  }
  
  .stage-title {
    font-size: 22px !important;
  }
  
  .video-card {
    padding: 18px;
  }
  
  .stage-header {
    margin-bottom: 18px;
  }
}

/* 响应式设计 - 基于屏幕高度 */
@media screen and (max-height: 700px) {
  .stage-container {
    padding: 25px;
  }
  
  .stage-title {
    font-size: 20px;
  }
  
  .video-card {
    padding: 15px;
  }
  
  .stage-header {
    margin-bottom: 15px;
  }
}

@media screen and (max-height: 600px) {
  .stage-container {
    padding: 20px;
  }
  
  .stage-title {
    font-size: 18px;
  }
  
  .video-card {
    padding: 12px;
  }
  
  .stage-header {
    margin-bottom: 12px;
  }
}

@media screen and (max-height: 500px) {
  .stage-container {
    padding: 15px;
  }
  
  .stage-title {
    font-size: 16px;
  }
  
  .video-card {
    padding: 10px;
  }
  
  .stage-header {
    margin-bottom: 10px;
  }
}

@media screen and (max-height: 450px) {
  .stage-container {
    padding: 12px !important;
    font-size: 10px;
  }
  
  .stage-title {
    font-size: 14px !important;
  }
  
  .video-card {
    padding: 8px !important;
  }
  
  .stage-header {
    margin-bottom: 8px !important;
  }
}

@media screen and (max-height: 380px) {
  .stage-container {
    padding: 8px !important;
    font-size: 9px;
  }
  
  .stage-title {
    font-size: 12px !important;
  }
  
  .video-card {
    padding: 6px !important;
  }
  
  .stage-header {
    margin-bottom: 6px !important;
  }
}

@media screen and (max-height: 320px) {
  .stage-container {
    padding: 5px !important;
    font-size: 8px;
  }
  
  .stage-title {
    font-size: 10px !important;
  }
  
  .video-card {
    padding: 4px !important;
  }
  
  .stage-header {
    margin-bottom: 4px !important;
  }
}
</style>