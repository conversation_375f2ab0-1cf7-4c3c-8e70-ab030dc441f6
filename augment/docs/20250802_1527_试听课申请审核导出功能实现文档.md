# 试听课申请审核导出功能实现文档

## 功能概述

为试听课申请审核页面增加导出功能，允许admin和hr角色导出满足当前查询条件的所有数据。导出文件通过OSS上传后返回下载链接给前端。

## 实现方案

### 1. 后端实现

#### 1.1 Controller层
- **文件**: `CourseBookingReviewController.java`
- **新增接口**: `POST /course-booking-review/export`
- **权限控制**: 仅允许admin和hr角色访问
- **参数**: 使用与查询列表相同的请求参数`CourseBookingReviewDto.GetListReq`

#### 1.2 Facade层
- **文件**: `CourseBookingReviewFacadeImpl.java`
- **新增方法**: `exportCourseBookingApplications()`
- **功能**: 
  - 权限检查（仅admin和hr可导出）
  - 复用现有查询逻辑获取数据
  - 生成Excel文件
  - 上传到OSS
  - 返回下载链接

#### 1.3 DTO层
- **文件**: `CourseBookingReviewDto.java`
- **新增类**: `ExportResp`
- **字段**: downloadUrl, fileName, totalCount, message

#### 1.4 Excel生成
- **使用技术**: Apache POI (XSSFWorkbook)
- **表头字段**: 申请ID、学生姓名、学生手机、学科、课型、销售姓名、销售组、首选教师、教学组、状态、优先级、申请时间、审核时间、审核人、拒绝原因、申请原因
- **样式**: 标题行蓝色背景，自动调整列宽

#### 1.5 OSS上传
- **存储路径**: `exports/course-booking-applications/`
- **文件命名**: `试听课申请列表_yyyyMMdd_HHmmss.xlsx`
- **返回**: OSS访问URL

### 2. 前端实现

#### 2.1 API接口
- **文件**: `courseReview.js`
- **新增方法**: `exportCourseBookingApplicationsApi()`
- **请求方式**: POST
- **路径**: `/course-booking-review/export`

#### 2.2 页面组件
- **文件**: `course-booking/index.vue`
- **新增功能**:
  - 导出按钮（仅admin和hr可见）
  - 权限检查计算属性
  - 导出方法实现
  - 加载状态管理

#### 2.3 权限控制
- **检查逻辑**: 基于用户角色判断是否显示导出按钮
- **角色要求**: admin 或 hr
- **实现方式**: Vue computed属性

#### 2.4 用户体验
- **导出流程**: 点击导出 → 显示加载状态 → 生成文件 → 自动下载
- **状态提示**: 导出中显示"导出中..."，完成后显示成功消息
- **错误处理**: 网络错误或权限错误的友好提示

## 技术特点

### 1. 权限安全
- 后端双重权限检查：注解级别 + 业务逻辑级别
- 前端权限控制：按钮显示控制
- 角色限制：仅admin和hr可使用

### 2. 性能优化
- 大数据量处理：设置10000条记录上限
- 异步处理：OSS上传不阻塞用户操作
- 内存管理：使用ByteArrayOutputStream避免临时文件

### 3. 用户体验
- 查询条件复用：导出数据与当前查询结果一致
- 进度反馈：实时显示导出状态
- 自动下载：生成完成后自动触发下载

### 4. 代码复用
- 查询逻辑复用：使用现有的查询条件和权限过滤
- 权限检查复用：使用现有的SystemDataQueryUtil
- 异常处理复用：使用BaseController的统一异常处理

## 文件清单

### 后端文件
1. `CourseBookingReviewController.java` - 新增导出接口
2. `ICourseBookingReviewFacade.java` - 新增导出方法声明
3. `CourseBookingReviewFacadeImpl.java` - 导出功能实现
4. `CourseBookingReviewDto.java` - 新增ExportResp类

### 前端文件
1. `courseReview.js` - 新增导出API
2. `course-booking/index.vue` - 新增导出按钮和功能

## 使用说明

### 1. 权限要求
- 用户必须具有admin或hr角色
- 需要`course:booking:query`权限

### 2. 操作步骤
1. 在试听课申请审核页面设置查询条件
2. 点击"导出数据"按钮
3. 等待文件生成（显示"导出中..."）
4. 文件生成完成后自动下载

### 3. 导出内容
- 导出当前查询条件下的所有数据（不分页）
- 最多导出10000条记录
- Excel格式，包含完整的申请信息

## 注意事项

1. **数据量限制**: 单次导出最多10000条记录
2. **权限控制**: 严格限制admin和hr角色使用
3. **查询条件**: 导出数据与当前页面查询条件保持一致
4. **文件存储**: 文件存储在OSS，需要确保OSS配置正确
5. **浏览器兼容**: 使用现代浏览器的下载API，确保兼容性

## 测试建议

1. **权限测试**: 验证非admin/hr用户无法看到导出按钮
2. **功能测试**: 验证导出数据与查询结果一致
3. **边界测试**: 测试大数据量导出性能
4. **错误测试**: 测试网络异常、OSS异常等错误场景
5. **兼容测试**: 测试不同浏览器的下载功能
