import { ref } from "vue";
import { ElLoading, ElMessage } from 'element-plus'

const currentAudio = ref<HTMLAudioElement | null>(null);

/**
 * LRU缓存类实现
 * @template K 键类型
 * @template V 值类型
 */
class LRUCache<K, V> {
  private capacity: number;
  private cache: Map<K, V>;
  private keys: K[];

  /**
   * 创建LRU缓存实例
   * @param capacity 缓存容量
   */
  constructor(capacity: number) {
    this.capacity = capacity;
    this.cache = new Map<K, V>();
    this.keys = [];
  }

  /**
   * 获取缓存项
   * @param key 键
   * @returns 缓存值或null
   */
  get(key: K): V | null {
    if (!this.cache.has(key)) return null;
    
    // 更新使用顺序（将访问的键移到最后，表示最近使用）
    this.keys = this.keys.filter(k => k !== key);
    this.keys.push(key);
    
    return this.cache.get(key) || null;
  }

  /**
   * 设置缓存项
   * @param key 键
   * @param value 值
   */
  set(key: K, value: V): void {
    // 如果已存在，先移除旧的使用记录
    if (this.cache.has(key)) {
      this.keys = this.keys.filter(k => k !== key);
    } 
    // 如果缓存已满，移除最久未使用的项
    else if (this.keys.length >= this.capacity) {
      const oldestKey = this.keys.shift();
      if (oldestKey !== undefined) {
        const oldValue = this.cache.get(oldestKey);
        // 如果是媒体元素，释放资源
        this.releaseResource(oldValue);
        this.cache.delete(oldestKey);
      }
    }
    
    // 添加新项到缓存和使用记录
    this.cache.set(key, value);
    this.keys.push(key);
  }

  /**
   * 检查键是否存在
   * @param key 键
   * @returns 是否存在
   */
  has(key: K): boolean {
    return this.cache.has(key);
  }

  /**
   * 删除缓存项
   * @param key 键
   */
  delete(key: K): void {
    if (this.cache.has(key)) {
      const value = this.cache.get(key);
      this.releaseResource(value);
      this.cache.delete(key);
      this.keys = this.keys.filter(k => k !== key);
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    // 释放所有资源
    this.cache.forEach(value => {
      this.releaseResource(value);
    });
    this.cache.clear();
    this.keys = [];
  }

  /**
   * 获取当前缓存大小
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * 释放资源（针对媒体元素）
   * @param value 资源值
   */
  private releaseResource(value: V | undefined): void {
    if (!value) return;
    
    // 处理音频元素
    if (value instanceof HTMLAudioElement) {
      value.pause();
      value.src = '';
      value.load(); // 强制浏览器释放资源
    }
    // 处理视频元素
    else if (value instanceof HTMLVideoElement) {
      value.pause();
      value.src = '';
      value.load(); // 强制浏览器释放资源
    }
  }
}

// 音频缓存池 - 最多缓存64个音频文件
const audioCache = new LRUCache<string, HTMLAudioElement>(64);
// 视频缓存池 - 最多缓存30个视频文件（视频通常较大）
const videoCache = new LRUCache<string, HTMLVideoElement>(30);

export function preloadAudioUtil(url?: string): HTMLAudioElement | null {
    if (!url) return null;
    
    // 检查缓存中是否已存在
    const cachedAudio = audioCache.get(url);
    if (cachedAudio) {
        return cachedAudio;
    }
    
    const audio = new Audio(url);
    audio.preload = 'auto';
    
    // 添加加载事件监听
    audio.addEventListener('canplaythrough', () => {
        console.log(`音频预加载完成: ${url}`);
    });
    
    audio.addEventListener('error', (e) => {
        audioCache.delete(url); // 删除失败的缓存
    });
    
    audio.load();
    audioCache.set(url, audio);
    return audio;
}

export function preloadVideoUtil(url?: string): HTMLVideoElement | null {
    // 确保URL有效
    if (!url || url.trim() === '') {
        console.warn('尝试预加载无效的视频URL');
        return null;
    }
    
    // 检查缓存中是否已存在
    const cachedVideo = videoCache.get(url);
    if (cachedVideo) {
        // 重置视频状态，准备复用
        cachedVideo.pause();
        cachedVideo.currentTime = 0;
        return cachedVideo;
    }
    
    try {
        const video = document.createElement('video');
        // 先设置事件监听，再设置src，避免空src错误
        video.addEventListener('canplaythrough', () => {
            console.log(`视频预加载完成: ${url}`);
        });
        
        video.addEventListener('error', (e) => {
            console.error(`视频预加载失败: ${url}`, e);
            videoCache.delete(url); // 删除失败的缓存
        });
        
        // 添加UI相关属性，使其可以直接用于展示
        video.style.maxWidth = '100%';
        video.style.maxHeight = '100%';
        video.preload = 'auto';
        video.muted = true; // 静音预加载，避免自动播放策略问题
        
        // 最后设置src并加载
        video.src = url;
        video.load();
        
        videoCache.set(url, video);
        return video;
    } catch (error) {
        console.error(`创建视频元素失败: ${url}`, error);
        return null;
    }
}

/**
 * 批量预加载视频文件
 * @param urls 视频URL数组
 * @returns Promise<HTMLVideoElement[]>
 */
export function preloadMultipleVideos(urls: (string | undefined)[]): Promise<HTMLVideoElement[]> {
    const validUrls = urls.filter(url => url && url.trim() !== '') as string[];
    
    const promises = validUrls.map(url => {
        return new Promise<HTMLVideoElement>((resolve, reject) => {
            const video = preloadVideoUtil(url);
            if (!video) {
                reject(new Error(`无效的视频URL: ${url}`));
                return;
            }
            
            if (video.readyState >= 4) {
                // 视频已经加载完成
                resolve(video);
            } else {
                // 等待视频加载完成
                const onCanPlay = () => {
                    video.removeEventListener('canplaythrough', onCanPlay);
                    video.removeEventListener('error', onError);
                    resolve(video);
                };
                
                const onError = (e: Event) => {
                    video.removeEventListener('canplaythrough', onCanPlay);
                    video.removeEventListener('error', onError);
                    reject(new Error(`视频加载失败: ${url}`));
                };
                
                video.addEventListener('canplaythrough', onCanPlay);
                video.addEventListener('error', onError);
            }
        });
    });
    
    return Promise.allSettled(promises).then(results => {
        const successfulVideos: HTMLVideoElement[] = [];
        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                successfulVideos.push(result.value);
            } else {
                console.warn(`视频预加载失败: ${validUrls[index]}`, result.reason);
            }
        });
        return successfulVideos;
    });
}

/**
 * 批量预加载音频文件
 * @param urls 音频URL数组
 * @returns Promise<HTMLAudioElement[]>
 */
export function preloadMultipleAudios(urls: (string | undefined)[]): Promise<HTMLAudioElement[]> {
    const validUrls = urls.filter(url => url && url.trim() !== '') as string[];
    
    const promises = validUrls.map(url => {
        return new Promise<HTMLAudioElement>((resolve, reject) => {
            const audio = preloadAudioUtil(url);
            if (!audio) {
                reject(new Error(`无效的音频URL: ${url}`));
                return;
            }
            
            if (audio.readyState >= 4) {
                // 音频已经加载完成
                resolve(audio);
            } else {
                // 等待音频加载完成
                const onCanPlay = () => {
                    audio.removeEventListener('canplaythrough', onCanPlay);
                    audio.removeEventListener('error', onError);
                    resolve(audio);
                };
                
                const onError = (e: Event) => {
                    audio.removeEventListener('canplaythrough', onCanPlay);
                    audio.removeEventListener('error', onError);
                    reject(new Error(`音频加载失败: ${url}`));
                };
                
                audio.addEventListener('canplaythrough', onCanPlay);
                audio.addEventListener('error', onError);
            }
        });
    });
    
    return Promise.allSettled(promises).then(results => {
        const successfulAudios: HTMLAudioElement[] = [];
        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                successfulAudios.push(result.value);
            } else {
                console.warn(`音频预加载失败: ${validUrls[index]}`, result.reason);
            }
        });
        return successfulAudios;
    });
}

/**
 * 组合式函数：用于组件中的音频预加载
 * @param audioUrls 音频URL对象或数组
 * @returns 预加载状态和方法
 */
export function useAudioPreloader(audioUrls: Record<string, string | undefined> | (string | undefined)[]) {
    const isLoading = ref(true);
    const loadedCount = ref(0);
    const totalCount = ref(0);
    const errors = ref<string[]>([]);
    
    const preloadAudios = async () => {
        isLoading.value = true;
        errors.value = [];
        
        let urls: (string | undefined)[];
        if (Array.isArray(audioUrls)) {
            urls = audioUrls;
        } else {
            urls = Object.values(audioUrls);
        }
        
        totalCount.value = urls.filter(url => url && url.trim() !== '').length;
        loadedCount.value = 0;
        
        try {
            await preloadMultipleAudios(urls);
            loadedCount.value = totalCount.value;
        } catch (error) {
            errors.value.push(error instanceof Error ? error.message : '未知错误');
        } finally {
            isLoading.value = false;
        }
    };
    
    const getPreloadedAudio = (url: string): HTMLAudioElement | null => {
        return audioCache.get(url) || null;
    };
    
    const clearCache = () => {
        audioCache.clear();
    };
    
    return {
        isLoading: readonly(isLoading),
        loadedCount: readonly(loadedCount),
        totalCount: readonly(totalCount),
        errors: readonly(errors),
        preloadAudios,
        getPreloadedAudio,
        clearCache
    };
}

/**
 * 组合式函数：用于组件中的视频预加载
 * @param videoUrls 视频URL对象或数组
 * @returns 预加载状态和方法
 */
export function useVideoPreloader(videoUrls: Record<string, string | undefined> | (string | undefined)[]) {
    const isLoading = ref(true);
    const loadedCount = ref(0);
    const totalCount = ref(0);
    const errors = ref<string[]>([]);
    
    const preloadVideos = async () => {
        isLoading.value = true;
        errors.value = [];
        
        let urls: (string | undefined)[];
        if (Array.isArray(videoUrls)) {
            urls = videoUrls;
        } else {
            urls = Object.values(videoUrls);
        }
        
        totalCount.value = urls.filter(url => url && url.trim() !== '').length;
        loadedCount.value = 0;
        
        try {
            await preloadMultipleVideos(urls);
            loadedCount.value = totalCount.value;
        } catch (error) {
            errors.value.push(error instanceof Error ? error.message : '未知错误');
        } finally {
            isLoading.value = false;
        }
    };
    
    const getPreloadedVideo = (url: string): HTMLVideoElement | null => {
        return videoCache.get(url) || null;
    };
    
    const clearCache = () => {
        videoCache.clear();
    };
    
    return {
        isLoading: readonly(isLoading),
        loadedCount: readonly(loadedCount),
        totalCount: readonly(totalCount),
        errors: readonly(errors),
        preloadVideos,
        getPreloadedVideo,
        clearCache
    };
}

export function playAudioUtil(url?: string) {
    // 停止当前音频
    if (currentAudio.value) {
        currentAudio.value.pause();
        currentAudio.value.currentTime = 0;
        currentAudio.value = null;
    }

    // 播放新音频
    if (url) {
        let audio: HTMLAudioElement;
        
        // 优先使用缓存中的音频
        const cachedAudio = audioCache.get(url);
        if (cachedAudio) {
            audio = cachedAudio;
            // 重置播放位置
            audio.currentTime = 0;
        } else {
            // 如果缓存中没有，创建新的音频对象
            audio = new Audio(url);
            audio.preload = 'auto';
            // 将新创建的音频添加到缓存
            audioCache.set(url, audio);
        }
        
        audio.play().catch(err => {
            console.error("音频播放失败：", err);
            ElMessage.error('音频播放失败，请检查网络连接');
        });
        
        currentAudio.value = audio;
    }
}

export function getRandomStarStyle() {
    const x = Math.random() * 100
    const y = Math.random() * 100
    const scale = 0.5 + Math.random() * 0.5
    const delay = Math.random() * 0.5
    return {
        left: `${x}%`,
        top: `${y}%`,
        transform: `scale(${scale})`,
        animationDelay: `${delay}s`
    }
}

export function getOptionLabel(index: number) {
    return String.fromCharCode(65 + index)
}
export function getOptionIndex(label: string): number {
    return label.charCodeAt(0) - 65;
}

export function loading(text: string = '处理中...'): any {
    return ElLoading.service({
        lock: true,
        text: text,
        background: 'rgba(0, 0, 0, 0.7)'
    })
}
