package org.nonamespace.word.server.dto.management.booking;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * 预约课审核管理DTO
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class CourseBookingReviewDto {

    /**
     * 分页查询请求
     */
    @Data
    public static class GetListReq {
        private Integer pageNum = 1;
        private Integer pageSize = 10;

        // 搜索条件
        private String studentName;        // 学生姓名
        private String subject;           // 学科
        private String courseType;        // 课型
        private String status;            // 状态
        private String priority;          // 优先级
        private String teachingGroupId;   // 教学组ID
        private String teacherName;       // 教师姓名
        private String salesGroupId;      // 销售组ID
        private String salesId;           // 销售人员ID
        private String teachingGroupManagerUserId; // 教学组管理员用户ID（内部使用）
        private Date createTimeStart;     // 创建时间开始
        private Date createTimeEnd;       // 创建时间结束
        private Date reviewTimeStart;     // 审核时间开始
        private Date reviewTimeEnd;       // 审核时间结束
    }

    /**
     * 列表响应
     */
    @Data
    public static class ListResp {
        private String id;
        private String studentId;
        private String studentName;
        private String studentPhone;
        private String subject;
        private String courseType;
        private String priority;
        private String status;
        private String statusText;

        // 申请信息
        private String applyReason;
        private List<String> preferredTeachers;
        private List<String> preferredTeacherNames;
        private String teachingGroupId;
        private String teachingGroupName;

        // 销售信息
        private String salesId;           // 销售人员ID
        private String salesName;         // 销售人员姓名
        private String salesGroupId;      // 销售组ID
        private String salesGroupName;    // 销售组名称
        
        // 审核信息
        private String reviewResult;
        private String reviewComment;
        private String rejectionReason;
        private String reviewerId;
        private String reviewerName;
        private Date reviewTime;
        
        // 分配信息
        private String assignedTeacherId;
        private String assignedTeacherName;

        // 时间信息
        private Date createTime;
        private Date updateTime;

        // 权限标识
        private Boolean canReview;  // 当前用户是否可以审核
    }

    /**
     * 详情响应
     */
    @Data
    public static class DetailResp {
        private String id;
        private String studentId;
        private String studentName;
        private String studentPhone;
        private String studentGrade;
        private String subject;
        private String courseType;
        private String priority;
        private String status;
        private String statusText;
        
        // 申请详细信息
        private String applyReason;
        private List<TeacherInfo> preferredTeachers;
        private List<TimeSlotInfo> preferredTimeSlots;
        private String teachingGroupId;
        private String teachingGroupName;
        
        // 审核信息
        private String reviewResult;
        private String reviewComment;
        private String rejectionReason;
        private String reviewerId;
        private String reviewerName;
        private Date reviewTime;

        // 作废信息
        private String voidOperatorId;    // 作废操作人员ID
        private String voidOperatorName;  // 作废操作人员姓名
        private Date voidTime;            // 作废时间
        private String voidReason;        // 作废原因
        
        // 分配信息
        private String assignedTeacherId;
        private String assignedTeacherName;
        private TrialTimeSlotInfo confirmedTimeSlot; // 确认的时间段（组长审核时选择的具体时间）
        
        // 销售信息
        private String salesId;
        private String salesName;
        private String salesGroupName;
        
        // 试听课时间信息
        private TrialClassTimeInfo trialClassTime;

        // 时间信息
        private Date createTime;
        private Date updateTime;

        // 审核情况
        private List<ReviewStatusInfo> reviewStatusList;

        // 权限标识
        private Boolean canReview;  // 当前用户是否可以审核
    }

    /**
     * 教师信息
     */
    @Data
    public static class TeacherInfo {
        private String teacherId;
        private String teacherName;
        private String phone;
        private String teachingGroupName;
        private String status;
    }

    /**
     * 时间段信息
     */
    @Data
    public static class TimeSlotInfo {
        private Integer weekday; // 星期几 (1-7, 1=周一, 7=周日)
        private String startTime; // 开始时间 (HH:mm格式)
        private String endTime; // 结束时间 (HH:mm格式)
    }

    /**
     * 试听课时间信息
     */
    @Data
    public static class TrialClassTimeInfo {
        private String date;      // 试听课日期 (YYYY-MM-DD格式)
        private String startTime; // 开始时间 (HH:mm格式)
        private String endTime;   // 结束时间 (HH:mm格式)
    }

    /**
     * 确认时间段信息（组长审核时选择的具体时间）
     */
    @Data
    public static class TrialTimeSlotInfo {
        private String date;      // 确认的试听课日期 (YYYY-MM-DD格式)
        private String startTime; // 确认的开始时间 (HH:mm格式)
        private String endTime;   // 确认的结束时间 (HH:mm格式)
    }

    /**
     * 审核状态信息
     */
    @Data
    public static class ReviewStatusInfo {
        private String teachingGroupId;   // 教学组ID
        private String teachingGroupName; // 教学组名称
        private String reviewStatus;      // 审核状态：待审核/已通过/已拒绝
        private String reviewTime;        // 审核时间 (YYYY-MM-DD HH:mm:ss格式)
        private String reviewerId;        // 审核人员ID
        private String reviewerName;      // 审核人员姓名
        private String reviewComment;     // 审核备注
        private Integer sortOrder;        // 排序顺序
    }

    /**
     * 审核请求
     */
    @Data
    public static class ReviewReq {
        @NotBlank(message = "申请ID不能为空")
        private String applicationId;
        
        @NotBlank(message = "审核结果不能为空")
        private String reviewResult;  // approved, rejected
        
        private String reviewComment;
        private String rejectionReason;
        private String assignedTeacherId;  // 审核通过时分配的教师
    }

    /**
     * 批量审核请求
     */
    @Data
    public static class BatchReviewReq {
        @NotEmpty(message = "申请ID列表不能为空")
        private List<String> applicationIds;
        
        @NotBlank(message = "审核结果不能为空")
        private String reviewResult;  // approved, rejected
        
        private String reviewComment;
        private String rejectionReason;
    }

    /**
     * 批量审核响应
     */
    @Data
    public static class BatchReviewResp {
        private Integer totalCount;
        private Integer successCount;
        private Integer failedCount;
        private List<String> failedApplicationIds;
        private List<String> failedReasons;
    }

    /**
     * 统计信息响应
     */
    @Data
    public static class StatsResp {
        private Long totalApplications;      // 总申请数
        private Long pendingApplications;    // 待审核申请数
        private Long approvedApplications;   // 已通过申请数
        private Long rejectedApplications;   // 已拒绝申请数
        private Long todayApplications;      // 今日申请数
        private Long todayReviewed;          // 今日已审核数
        private Double approvalRate;         // 通过率
        private Long overdueApplications;    // 超时未审核数
        
        // 教学组长专用统计
        private Long myGroupApplications;    // 我的教学组申请数
        private Long myGroupPending;         // 我的教学组待审核数
    }

    /**
     * 权限信息响应
     */
    @Data
    public static class PermissionResp {
        private String userRole;             // 用户角色
        private Boolean canReview;           // 是否可以审核
        private Boolean canViewAll;          // 是否可以查看所有申请
        private List<String> managedGroupIds; // 管理的教学组ID列表
        private String currentGroupId;       // 当前教学组ID（教学组长）
        private String currentGroupName;     // 当前教学组名称（教学组长）
    }

    /**
     * 可分配教师响应
     */
    @Data
    public static class AvailableTeacherResp {
        private String teacherId;
        private String teacherName;
        private String phone;
        private String teachingGroupId;
        private String teachingGroupName;
        private String status;
        private String employmentType;
        private List<String> subjects;       // 擅长学科
    }

    /**
     * 导出响应
     */
    @Data
    public static class ExportResp {
        private String downloadUrl;          // 下载链接
        private String fileName;             // 文件名
        private Integer totalCount;          // 导出记录总数
        private String message;              // 提示信息
    }
}
