<template>
  <div class="stage-container">
    <div class="stage-header">
      <div class="stage-title">{{ (stepIndex || 0) + 1 }}. 【单词通关】（1）划分音节（2）听两种发音（3）理解释义 （4）大声模仿跟读</div>
    </div>

    <div class="stage-content">
      <div class="word-display">
        <span class="word-display-text">{{ currentStepInfo?.wordInfo?.word }}</span>
        <span class="pronunciation uk-pronunciation"
          @click="playAudio(currentStepInfo?.wordInfo?.audioUkUrl)">英式 {{ currentStepInfo?.wordInfo?.phoneticUk }}
          <el-icon>
            <VideoPlay />
          </el-icon></span>
        <span class="pronunciation us-pronunciation" @click="playAudio(currentStepInfo?.wordInfo?.audioUsUrl)">美式 {{
          currentStepInfo?.wordInfo?.phoneticUs }} <el-icon>
            <VideoPlay />
          </el-icon></span>
      </div>
      <div class="word-card">
        <div class="word-details">
          <div class="meaning">
            <span class="detail-label">释义:</span>
            <div v-for="(item, index) in currentStepInfo?.wordInfo?.meanings.pos" :key="index">
              {{ item.pos }}. {{ item.def }}
            </div>
          </div>

<!--          <div class="example">-->
<!--            <div class="detail-label">例句:</div>-->
<!--            {{ currentStepInfo?.wordInfo?.sentences?.sentenceEn }}<br>-->
<!--            {{ currentStepInfo?.wordInfo?.sentences?.sentenceCn }}-->
<!--          </div>-->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {defineProps, ref, onUnmounted} from 'vue'
import {VideoPlay} from '@element-plus/icons-vue'
import {COURSE_sessionStorage_INFO_KEY, CurrentStepInfo, getStepInfoByType, submitCourseStepApi} from "@/api/course";
import {playAudioUtil, useAudioPreloader} from '@/api/course/util'

const emit = defineEmits(['complete'])
const props = defineProps({
  selectWordText: String,
  courseId: {
    type: [String, Number],
    required: true
  },
  stepIndex: Number,
  sectionId: String
})
const currentStepInfo = ref<CurrentStepInfo | null>(null);

// 音频预加载相关
let audioPreloader: ReturnType<typeof useAudioPreloader> | null = null;
const playAudio = (url:string) => {
  playAudioUtil(url)
}

/**
 * 提交课程步骤
 */
const submitCourseStep = () => {
  // 如果是已完成状态，不用重复提交
  let status = currentStepInfo.value.step.status
  if (status === "已完成") {
    return;
  }
  let wordId = currentStepInfo.value.wordId

  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: "正确",
    studentAnswer: "正确"
  }
  submitCourseStepApi(props.courseId, props.sectionId, wordId, submitResult, true)
}

onMounted(async () => {
  // 先从sessionStorage获取
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    try {
      let courseInfo = JSON.parse(obj);
      currentStepInfo.value = await getStepInfoByType(courseInfo, '单词讲解', props.selectWordText)
      
      // 预加载音频文件
      if (currentStepInfo.value?.wordInfo) {
        const audioUrls = [
          currentStepInfo.value.wordInfo.audioUkUrl,
          currentStepInfo.value.wordInfo.audioUsUrl
        ].filter(url => url && url.trim() !== '');
        
        if (audioUrls.length > 0) {
          console.log('开始预加载单词讲解音频文件:', audioUrls);
          audioPreloader = useAudioPreloader(audioUrls);
          await audioPreloader.preloadAudios();
          console.log('单词讲解音频预加载完成');
        }
      }
      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }
})

// 不再需要在组件卸载时清除缓存，因为已在切换单词时清理
onUnmounted(() => {
  // 缓存清理已移至index.vue中的goToNextWord和changeCurrentStageType函数
})

defineExpose({
  submitCourseStep
})
</script>

<style scoped>
.stage-container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  padding: 40px;
}

.stage-header {
  width: 100%;
}

.stage-title {
  font-size: 24px;
  font-weight: bold;
  color: #5d4037;
}

.word-card {
  width: 100%;
  padding: 10px 30px 30px 30px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-top: 20px;
}

.pronunciation span {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pronunciation span:hover {
  color: #FF9800;
  transform: translateY(-2px);
}

.pronunciation .el-icon {
  margin-left: 8px;
  color: #FF9800;
  font-size: 26px;
  display: inline-flex;
  align-items: center;
}

.detail-label {
  font-weight: bold;
  color: #FF9800;
  margin-right: 10px;
  font-size: 30px;
}

.meaning {
  font-size: 20px;
  line-height: 1.8;
  color: #5d4037;
}

.meaning div {
  margin-bottom: 10px;
  padding: 5px 0;
  border-bottom: 1px dashed #f2ad47;
}

.example {
  font-size: 18px;
  color: #5d4037;
  line-height: 1.8;
  padding: 15px;
  background-color: #fff9e6;
  border-radius: 8px;
  border-left: 4px solid #f2ad47;
}

.stage-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-height: 0;
}

.word-display {
  font-size: 70px;
  font-weight: bold;
  color: #5d4037;
  font-family: "Comic Sans MS", cursive, sans-serif;
}

.word-display-text {
  font-family: "Comic Sans MS", cursive, sans-serif;
  margin-right: 40px;
}

.pronunciation {
  font-size: 26px;
  cursor: pointer;
  align-items: center;
  display: inline-flex;
  transition: all 0.3s ease;
}

.uk-pronunciation {
  margin-right: 40px;
}

.pronunciation:hover {
  color: #FF9800;
  transform: translateY(-2px);
}

.pronunciation .el-icon {
  margin-left: 8px;
  color: #FF9800;
  font-size: 26px;
  display: inline-flex;
  align-items: center;
}

/* 响应式设计 - 基于屏幕高度 */
@media screen and (max-height: 700px) {
  .stage-container {
    padding: 20px 15px;
  }
  
  .stage-title {
    font-size: 20px;
  }
  
  .word-display {
    font-size: 50px;
  }
  
  .word-display-text {
    margin-right: 30px;
  }
  
  .pronunciation {
    font-size: 26px;
  }
  
  .uk-pronunciation {
    margin-right: 30px;
  }
  
  .pronunciation .el-icon {
    font-size: 26px;
    display: inline-flex;
    align-items: center;
  }
  
  .word-card {
    padding: 10px 20px 20px 20px;
    margin-top: 15px;
  }
  
  .detail-label {
    font-size: 18px;
  }
  
  .meaning {
    font-size: 18px;
  }
}

@media screen and (max-height: 500px) {
  .stage-container {
    padding: 15px 10px;
  }
  
  .stage-title {
    font-size: 18px;
  }
  
  .word-display {
    font-size: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .word-display-text {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .pronunciation {
    font-size: 26px;
  }
  
  .uk-pronunciation {
    margin-right: 0;
    margin-bottom: 5px;
  }
  
  .pronunciation .el-icon {
    font-size: 24px;
    display: inline-flex;
    align-items: center;
  }
  
  .word-card {
    padding: 8px 15px 15px 15px;
    margin-top: 10px;
  }
  
  .detail-label {
    font-size: 16px;
  }
  
  .meaning {
    font-size: 16px;
    line-height: 1.6;
  }
  
  .meaning div {
    margin-bottom: 8px;
    padding: 3px 0;
  }
}

/* 极小屏幕高度 - 启用滚动条 */
@media screen and (max-height: 400px) {
  .stage-container {
    padding: 10px 8px;
    overflow-y: auto; /* 在极小屏幕下启用滚动条 */
  }
  
  .stage-title {
    font-size: 16px;
  }
  
  .word-display {
    font-size: 32px;
  }
  
  .pronunciation {
    font-size: 12px;
  }
  
  .pronunciation .el-icon {
    font-size: 22px;
    display: inline-flex;
    align-items: center;
  }
  
  .word-card {
    padding: 5px 10px 10px 10px;
    margin-top: 8px;
  }
  
  .detail-label {
    font-size: 14px;
  }
  
  .meaning {
    font-size: 14px;
    line-height: 1.4;
  }
  
  .meaning div {
    margin-bottom: 5px;
    padding: 2px 0;
  }
}

/* 动态字体缩放以避免滚动 */
@media (max-height: 800px) {
  .stage-container {
    font-size: 14px;
  }
  
  .stage-title {
    font-size: 16px !important;
  }
  
  .word-display {
    font-size: 40px !important;
  }
  
  .pronunciation {
    font-size: 26px !important;
  }
  
  .pronunciation .el-icon {
    font-size: 24px !important;
    display: inline-flex !important;
    align-items: center !important;
  }
  
  .detail-label {
    font-size: 16px !important;
  }
  
  .meaning {
    font-size: 16px !important;
  }
}

@media (max-height: 700px) {
  .stage-container {
    font-size: 12px;
    padding: 15px 10px;
  }
  
  .stage-title {
    font-size: 15px !important;
  }
  
  .word-display {
    font-size: 36px !important;
  }
  
  .pronunciation {
    font-size: 15px !important;
  }
  
  .pronunciation .el-icon {
    font-size: 13px !important;
    display: inline-flex !important;
    align-items: center !important;
  }
  
  .word-card {
    padding: 8px 12px 12px 12px !important;
  }
  
  .detail-label {
    font-size: 15px !important;
  }
  
  .meaning {
    font-size: 15px !important;
  }
}

@media (max-height: 600px) {
  .stage-container {
    font-size: 11px;
    padding: 12px 8px;
  }
  
  .stage-title {
    font-size: 14px !important;
  }
  
  .word-display {
    font-size: 32px !important;
  }
  
  .pronunciation {
    font-size: 24px !important;
  }
  
  .pronunciation .el-icon {
    font-size: 22px !important;
    display: inline-flex !important;
    align-items: center !important;
  }
  
  .word-card {
    padding: 6px 10px 10px 10px !important;
  }
  
  .detail-label {
    font-size: 14px !important;
  }
  
  .meaning {
    font-size: 14px !important;
  }
}

@media (max-height: 500px) {
  .stage-container {
    font-size: 10px;
    padding: 10px 6px;
  }
  
  .stage-title {
    font-size: 13px !important;
  }
  
  .word-display {
    font-size: 28px !important;
  }
  
  .pronunciation {
    font-size: 16px !important;
  }
  
  .pronunciation .el-icon {
    font-size: 14px !important;
    display: inline-flex !important;
    align-items: center !important;
  }
  
  .word-card {
    padding: 5px 8px 8px 8px !important;
  }
  
  .detail-label {
    font-size: 13px !important;
  }
  
  .meaning {
    font-size: 13px !important;
  }
}

/* 动画关键帧 */
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}
</style>