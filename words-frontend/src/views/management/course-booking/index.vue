<template>
  <div class="course-review-page">
    <!-- 页面头部 -->
    <!-- <div class="page-header">
      <div class="header-content">
        <h2>预约课审核</h2>
        <p>教学组长审核预约课申请</p>
      </div>
      <div class="header-actions">
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div> -->

    <!-- 搜索条件 -->
    <el-card shadow="never" class="search-card">
      <el-form :model="searchForm" label-width="70px" size="small">
        <!-- 第一行：基础查询条件（始终显示） -->
        <el-row :gutter="16">
          <el-col :span="4">
            <el-form-item label="申请状态">
              <el-select
                v-model="searchForm.statusList"
                placeholder="请选择状态"
                clearable
                multiple
                style="width: 100%"
              >
                <el-option label="待审核" value="待审核" />
                <el-option label="已通过" value="已通过" />
                <el-option label="已拒绝" value="已拒绝" />
                <el-option label="已作废" value="已作废" />
                <el-option label="已取消" value="已取消" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="学生姓名">
              <el-input
                v-model="searchForm.studentName"
                placeholder="请输入学生姓名"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
              <el-form-item label="学生手机">
                <el-input
                  v-model="searchForm.studentPhone"
                  placeholder="请输入学生手机号"
                  clearable
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="老师姓名">
                <el-input
                  v-model="searchForm.teacherName"
                  placeholder="请输入老师姓名"
                  clearable
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          <el-col :span="8">
            <el-form-item label=" ">
              <div class="search-actions">
                <el-button type="primary" @click="handleSearch" icon="Search">
                  搜索
                </el-button>
                <el-button @click="handleReset" icon="Refresh">重置</el-button>
                <el-button
                  type="info"
                  text
                  @click="searchExpanded = !searchExpanded"
                  :icon="searchExpanded ? 'ArrowUp' : 'ArrowDown'"
                >
                  {{ searchExpanded ? '收起' : '展开' }}
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 展开的查询条件 -->
        <div v-show="searchExpanded" class="expanded-search">
          <!-- 第二行：联系方式查询 -->
          <el-row :gutter="16">
            <el-col :span="4">
              <el-form-item label="老师手机">
                <el-input
                  v-model="searchForm.teacherPhone"
                  placeholder="请输入老师手机号"
                  clearable
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
            <el-form-item label="学科">
              <el-select
                v-model="searchForm.subject"
                placeholder="请选择学科"
                clearable
                style="width: 100%"
                @change="handleSubjectChange"
              >
                <el-option label="英语" value="英语" />
                <el-option label="语文" value="语文" />
                <el-option label="数学" value="数学" />
                <el-option label="物理" value="物理" />
                <el-option label="化学" value="化学" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="课型">
              <el-select
                v-model="searchForm.specification"
                placeholder="请选择课型"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in availableSpecifications"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="销售组">
                <el-select
                  v-model="searchForm.salesGroupId"
                  placeholder="请选择销售组"
                  clearable
                  style="width: 100%"
                  @change="handleSalesGroupChange"
                >
                  <el-option
                    v-for="group in salesGroupOptions"
                    :key="group.id"
                    :label="group.name"
                    :value="group.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="销售人员">
                <el-select
                  v-model="searchForm.salesId"
                  placeholder="请选择销售人员"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="staff in salesStaffOptions"
                    :key="staff.id"
                    :label="staff.name"
                    :value="staff.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <!-- 占位，保持对齐 -->
            </el-col>
          </el-row>

          <!-- 第三行：申请时间范围 -->
          <el-row :gutter="16">
            <el-col :span="24">
              <el-form-item label="申请时间">
                <div class="time-range-container">
                  <!-- 快捷选择按钮 -->
                  <div class="quick-time-buttons">
                    <el-button
                      size="small"
                      :type="quickTimeType === 'today' ? 'primary' : ''"
                      @click="handleQuickTime('today')"
                    >
                      今日
                    </el-button>
                    <el-button
                      size="small"
                      :type="quickTimeType === 'yesterday' ? 'primary' : ''"
                      @click="handleQuickTime('yesterday')"
                    >
                      昨日
                    </el-button>
                    <el-button
                      size="small"
                      :type="quickTimeType === 'thisWeek' ? 'primary' : ''"
                      @click="handleQuickTime('thisWeek')"
                    >
                      本周
                    </el-button>
                    <el-button
                      size="small"
                      :type="quickTimeType === 'thisMonth' ? 'primary' : ''"
                      @click="handleQuickTime('thisMonth')"
                    >
                      本月
                    </el-button>
                  </div>
                  <!-- 自定义时间范围 -->
                  <el-date-picker
                    v-model="searchForm.createTimeRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 400px; margin-left: 10px"
                    @change="handleTimeRangeChange"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>

    <!-- 数据表格容器 -->
    <div class="table-container">
      <!-- 表格卡片 -->
      <el-card shadow="never" class="table-card">
        <!-- 表格操作栏 -->
        <div class="table-toolbar" v-if="hasAdminOrHrRole">
          <div class="toolbar-left">
            <span class="toolbar-title">试听课申请列表</span>
          </div>
          <div class="toolbar-right">
            <el-button
              type="primary"
              :loading="exporting"
              @click="handleExport"
              icon="Download"
            >
              {{ exporting ? '导出中...' : '导出数据' }}
            </el-button>
          </div>
        </div>
        <div class="table-wrapper">
          <el-table
            :data="applicationList"
            v-loading="loading"
            stripe
            :max-height="tableHeight"
            style="width: 100%"
            table-layout="auto"
          >
        <el-table-column prop="studentName" label="学生姓名" width="120" />
        <el-table-column prop="studentPhone" label="学生手机" width="130" />
        <el-table-column prop="subject" label="学科" width="80" />
        <el-table-column prop="specification" label="课型" width="100" />
        <el-table-column label="预约老师" min-width="240">
          <template #default="{ row }">
            <div class="preferred-teachers">
              <el-tag
                v-for="teacher in row.preferredTeacherInfos"
                :key="teacher.teacherId"
                :type="teacher.teacherId === row.approvedTeacherId ? 'success' : 'info'"
                size="small"
                class="teacher-tag"
              >
                <!-- 如果是已通过审核且是被选择的老师，显示绿色勾 -->
                <span v-if="teacher.teacherId === row.approvedTeacherId" class="approved-icon">✅</span>
                {{ teacher.teacherName }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <!-- 试听课时间列 -->
        <el-table-column label="试听课时间" min-width="160">
          <template #default="{ row }">
            <div v-if="row.trialClassTime" class="trial-time-info">
              <div class="trial-date">{{ formatTrialDate(row.trialClassTime.date) }}</div>
              <div class="trial-time">{{ formatTimeOnly(row.trialClassTime.startTime) }}-{{ formatTimeOnly(row.trialClassTime.endTime) }}</div>
            </div>
            <span v-else class="no-data">--</span>
          </template>
        </el-table-column>

        <el-table-column label="正课时间" min-width="200">
          <template #default="{ row }">
            <div class="preferred-times">
              <el-tag
                v-for="(timeSlot, index) in row.preferredTimeSlots"
                :key="index"
                type="info"
                size="small"
              >
                {{ getWeekdayText(timeSlot.weekday) }} {{ formatTimeOnly(timeSlot.startTime) }}-{{ formatTimeOnly(timeSlot.endTime) }}
              </el-tag>
              <span v-if="row.preferredTimeSlots?.length > 2" class="more-indicator">
                +{{ row.preferredTimeSlots.length - 2 }}个
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="salesGroupName" label="销售组" width="120" />
        <el-table-column prop="salesName" label="销售人员" width="120" />
        <el-table-column label="申请状态" width="150">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.statusText)"
              size="small"
            >
              <!-- {{ getStatusText(row.status) }} -->
              {{row.statusText}}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="申请时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" @click="handleViewDetail(row)" icon="View">
                查看详情
              </el-button>
              <!-- 只有具备审核权限才显示审核按钮 -->
              <template v-if="row.canReview">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleApprove(row)"
                  icon="Check"
                >
                  通过
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleReject(row)"
                  icon="Close"
                >
                  拒绝
                </el-button>
              </template>
              <!-- 作废按钮 -->
              <template v-if="canVoidApplication(row)">
                <el-button
                  v-hasRole="['admin', 'hr', 'sales']"
                  type="warning"
                  size="small"
                  @click="handleVoid(row)"
                  icon="Delete"
                  plain
                >
                  作废
                </el-button>
              </template>
              <!-- 已审核状态显示结果 -->
              <template v-else-if="row.status !== '待审核'">
                <el-tag
                  :type="getStatusType(row.status)"
                  size="small"
                  effect="plain"
                >
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </div>
          </template>
        </el-table-column>
          </el-table>
        </div>
      </el-card>

      <!-- 固定在底部的分页 -->
      <div class="fixed-pagination">
        <el-pagination
          :current-page="searchForm.pageNum"
          :page-size="searchForm.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentPageChange"
        />
      </div>
    </div>

    <!-- 申请详情对话框 -->
    <ApplicationDetailDialog
      v-model="showDetailDialog"
      :application-id="selectedApplicationId"
      @refresh="handleSearch"
    />

    <!-- 审核对话框 -->
    <ReviewDialog
      v-model="showReviewDialog"
      :application="selectedApplication"
      :review-type="reviewType"
      @success="handleReviewSuccess"
    />


  </div>
</template>

<script setup name="course-booking">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  ArrowUp,
  ArrowDown,
  View,
  Check,
  Close,
  Document,
  Download
} from '@element-plus/icons-vue'
import {
  getPendingApplicationsApi,
  exportCourseBookingApplicationsApi
} from '@/api/teaching-group-leader/courseReview'
import {
  getSalesGroupOptionsApi,
  getSalesStaffOptionsApi
} from '@/api/management/courseBookingApplication'
import request from '@/utils/request'
import { checkPermi } from '@/utils/permission'
import useUserStore from '@/store/modules/user'
import ApplicationDetailDialog from './components/ApplicationDetailDialog.vue'
import ReviewDialog from './components/ReviewDialog.vue'

// 响应式数据
const loading = ref(false)
const applicationList = ref([])
const total = ref(0)
const selectedApplication = ref({})
const selectedApplicationId = ref('')
const showDetailDialog = ref(false)
const showReviewDialog = ref(false)
const reviewType = ref('approve') // approve, reject
const searchExpanded = ref(false) // 搜索条件展开状态
const tableHeight = ref(600) // 表格高度
const exporting = ref(false) // 导出状态

// 销售相关选项数据
const salesGroupOptions = ref([])
const salesStaffOptions = ref([])

// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(allSpecifications)

// 学科变化处理
const handleSubjectChange = (subject) => {
  // 清空当前选择的课型
  searchForm.specification = ''

  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 20,
  studentName: '',
  studentPhone: '',
  teacherName: '',
  teacherPhone: '',
  statusList: ['待审核'], // 默认状态为待审核
  subject: '',
  specification: '',
  salesGroupId: '', // 销售组ID
  salesId: '', // 销售人员ID
  approvalResult: '', // 审核结果查询条件
  createTimeRange: [], // 申请时间范围
  createTimeStart: '', // 申请开始时间
  createTimeEnd: '' // 申请结束时间
})

// 快捷时间选择状态
const quickTimeType = ref('')

// 权限检查
const userStore = useUserStore()
const hasAdminOrHrRole = computed(() => {
  return userStore.roles.includes('admin') || userStore.roles.includes('hr')
})

// 方法
function handleSearch() {
  loadApplicationList()
}

function handleReset() {
  Object.assign(searchForm, {
    pageNum: 1,
    pageSize: 20,
    studentName: '',
    studentPhone: '',
    teacherName: '',
    teacherPhone: '',
    statusList: ['待审核'], // 重置时恢复默认状态
    subject: '',
    specification: '',
    salesGroupId: '', // 重置销售组
    salesId: '', // 重置销售人员
    approvalResult: '',
    createTimeRange: [],
    createTimeStart: '',
    createTimeEnd: ''
  })
  quickTimeType.value = ''
  // 重置销售人员选项
  loadSalesStaffOptions()
  handleSearch()
}

function handleRefresh() {
  handleSearch()
}

// 获取销售组选项
async function loadSalesGroupOptions() {
  try {
    const response = await getSalesGroupOptionsApi()
    salesGroupOptions.value = response.data || []
  } catch (error) {
    console.error('获取销售组选项失败:', error)
    salesGroupOptions.value = []
  }
}

// 获取销售人员选项
async function loadSalesStaffOptions(groupId = '') {
  try {
    const params = {}
    if (groupId) {
      params.groupId = groupId
    }
    const response = await getSalesStaffOptionsApi(params)
    salesStaffOptions.value = response.data || []
  } catch (error) {
    console.error('获取销售人员选项失败:', error)
    salesStaffOptions.value = []
  }
}

// 销售组变化处理
function handleSalesGroupChange(groupId) {
  // 清空销售人员选择
  searchForm.salesId = ''
  // 重新加载销售人员选项
  loadSalesStaffOptions(groupId)
}

// 分页处理
function handlePageSizeChange(size) {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  handleSearch()
}

function handleCurrentPageChange(page) {
  searchForm.pageNum = page
  handleSearch()
}

// 快捷时间选择
function handleQuickTime(type) {
  quickTimeType.value = type
  const now = new Date()
  let startTime, endTime

  switch (type) {
    case 'today':
      startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0)
      endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
      break
    case 'yesterday':
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      startTime = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 0, 0, 0)
      endTime = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59)
      break
    case 'thisWeek':
      const weekStart = new Date(now)
      const day = weekStart.getDay()
      const diff = weekStart.getDate() - day + (day === 0 ? -6 : 1) // 调整为周一开始
      weekStart.setDate(diff)
      startTime = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate(), 0, 0, 0)
      endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
      break
    case 'thisMonth':
      startTime = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0)
      endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
      break
    default:
      return
  }

  // 格式化时间
  const formatDateTime = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  searchForm.createTimeRange = [formatDateTime(startTime), formatDateTime(endTime)]
  searchForm.createTimeStart = formatDateTime(startTime)
  searchForm.createTimeEnd = formatDateTime(endTime)
}

// 自定义时间范围变化
function handleTimeRangeChange(value) {
  quickTimeType.value = '' // 清除快捷选择状态
  if (value && value.length === 2) {
    searchForm.createTimeStart = value[0]
    searchForm.createTimeEnd = value[1]
  } else {
    searchForm.createTimeStart = ''
    searchForm.createTimeEnd = ''
  }
}

function handleViewDetail(application) {
  selectedApplication.value = application
  selectedApplicationId.value = application.id
  showDetailDialog.value = true
}

function handleApprove(application) {
  selectedApplication.value = application
  reviewType.value = 'approve'
  showReviewDialog.value = true
}

function handleReject(application) {
  selectedApplication.value = application
  reviewType.value = 'reject'
  showReviewDialog.value = true
}

function handleReviewSuccess() {
  ElMessage.success('审核成功')
  handleSearch()
}

// 检查是否可以作废申请
function canVoidApplication(application) {
  // 只有待审核和已通过状态可以作废
  if (application.status !== '待审核' && application.status !== '已通过') {
    return false
  }

  // 根据用户角色和申请状态判断权限
  const userRoles = getUserRoles() // 需要实现获取用户角色的方法
  console.log('=========================', application, userRoles)

  if (application.status === '待审核') {
    // 待审核状态：销售可以作废自己的申请
    return userRoles.includes('sales') || userRoles.includes('admin') || userRoles.includes('hr')
  } else if (application.status === '已通过') {
    // 已通过状态：只有admin和hr可以作废
    return userRoles.includes('admin') || userRoles.includes('hr')
  }

  return false
}

// 获取用户角色（简化实现，实际应该从store或API获取）
function getUserRoles() {
  // 这里应该从用户信息中获取角色
  // 暂时返回示例角色，实际需要根据项目的用户管理方式实现
  return ['admin'] // 示例：返回admin角色
}

// 处理作废申请
function handleVoid(application) {
  ElMessageBox.confirm(
    `确定要作废申请"${application.studentName}"的预约课申请吗？`,
    '作废确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          // 显示作废原因输入框
          ElMessageBox.prompt('请输入作废原因', '作废原因', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputPattern: /.+/,
            inputErrorMessage: '作废原因不能为空'
          }).then(({ value }) => {
            // 调用作废API
            voidApplication(application.id, value)
            done()
          }).catch(() => {
            done()
          })
        } else {
          done()
        }
      }
    }
  ).catch(() => {
    // 用户取消
  })
}

// 调用作废API
async function voidApplication(applicationId, voidReason) {
  try {
    loading.value = true
    const response = await request({
      url: `/course-booking/${applicationId}/void`,
      method: 'post',
      data: {
        voidReason: voidReason
      }
    })

    if (response.code === 200) {
      ElMessage.success('申请作废成功')
      handleSearch() // 刷新列表
    }
  } catch (error) {
    console.error('作废申请失败:', error)
  } finally {
    loading.value = false
  }
}

async function loadApplicationList() {
  try {
    loading.value = true

    // 恢复使用原来的API
    const response = await getPendingApplicationsApi(searchForm)

    // 处理IPage格式的数据
    if (response.data && typeof response.data === 'object' && response.data.records) {
      // 后端返回的是IPage格式
      applicationList.value = response.data.records || []
      total.value = response.data.total || 0
    } else {
      // 兼容其他格式
      applicationList.value = response.data || []
      total.value = response.total || 0
    }
  } catch (error) {
    console.error('加载申请列表失败:', error)
  } finally {
    loading.value = false
  }
}



function getStatusType(status) {
    if(status.includes('已通过')) return 'success'
    if(status.includes('审核') && status.includes('拒绝')) return 'warning'
    if(status.includes('拒绝')) return 'danger' 
    return 'info'
}

function getStatusText(status) {
  const statusMap = {
    // 英文状态转中文（兼容）
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝',
    cancelled: '已取消',
    withdrawn: '已撤回',
    voided: '已作废',
    // 中文状态直接返回
    '待审核': '待审核',
    '已通过': '已通过',
    '已拒绝': '已拒绝',
    '已取消': '已取消',
    '已撤回': '已撤回',
    '已作废': '已作废'
  }
  return statusMap[status] || status
}

function getWeekdayText(weekday) {
  const weekdayMap = {
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六',
    7: '周日'
  }
  return weekdayMap[weekday] || `周${weekday}`
}

function formatDateTime(date) {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

function formatTrialDate(dateStr) {
  if (!dateStr) return '--'
  const date = new Date(dateStr)
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const weekday = weekdays[date.getDay()]
  return `${date.getMonth() + 1}月${date.getDate()}日 ${weekday}`
}

// 格式化时间为 HH:mm 格式（去掉秒）
function formatTimeOnly(timeStr) {
  if (!timeStr) return '--'
  // 如果是 HH:mm:ss 格式，截取前5位
  if (timeStr.includes(':') && timeStr.length > 5) {
    return timeStr.substring(0, 5)
  }
  return timeStr
}

// 导出数据
async function handleExport() {
  try {
    if (!hasAdminOrHrRole.value) {
      ElMessage.error('您没有导出权限')
      return
    }

    exporting.value = true
    ElMessage.info('正在生成导出文件，请稍候...')

    // 构建导出参数（使用当前搜索条件）
    const exportParams = {
      studentName: searchForm.studentName,
      studentPhone: searchForm.studentPhone,
      teacherName: searchForm.teacherName,
      teacherPhone: searchForm.teacherPhone,
      statusList: searchForm.statusList,
      subject: searchForm.subject,
      specification: searchForm.specification,
      salesGroupId: searchForm.salesGroupId,
      salesId: searchForm.salesId,
      approvalResult: searchForm.approvalResult,
      createTimeStart: searchForm.createTimeStart,
      createTimeEnd: searchForm.createTimeEnd
    }

    const response = await exportCourseBookingApplicationsApi(exportParams)

    if (response.data && response.data.downloadUrl) {
      // 创建下载链接
      const link = document.createElement('a')
      link.href = response.data.downloadUrl
      link.download = response.data.fileName || '试听课申请列表.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      ElMessage.success(`导出成功！共导出 ${response.data.totalCount} 条记录`)
    } else {
      ElMessage.error('导出失败：' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败：' + (error.message || '网络错误'))
  } finally {
    exporting.value = false
  }
}

// 计算表格高度
const calculateTableHeight = () => {
  // 计算可用高度：视窗高度 - 顶部padding - 搜索表单 - 固定分页 - 其他间距
  const windowHeight = window.innerHeight
  const usedHeight = 40 + 150 + 80 + 40 // 大概的固定高度
  tableHeight.value = Math.max(400, windowHeight - usedHeight)
}

// 生命周期
onMounted(() => {
  handleSearch()
  calculateTableHeight()

  // 加载销售相关选项
  loadSalesGroupOptions()
  loadSalesStaffOptions()

  // 监听窗口大小变化
  window.addEventListener('resize', calculateTableHeight)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight)
})
</script>

<style scoped>
.course-review-page {
  padding: 20px 20px 0 20px;
  background-color: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}



/* 搜索条件样式 */
.search-card {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.search-card .el-form {
  padding: 8px 0;
}

.search-card .el-form-item {
  margin-bottom: 12px;
}

.search-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.expanded-search {
  border-top: 1px solid #ebeef5;
  padding-top: 12px;
  margin-top: 8px;
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
  padding-bottom: 80px; /* 为固定分页留出空间 */
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  overflow: hidden;
  min-height: 0;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.table-wrapper .el-table {
  width: 100% !important;
}

.table-wrapper .el-table__body-wrapper {
  overflow-x: auto;
}

.table-wrapper .el-table .el-table__cell {
  padding: 8px 12px;
}

/* 固定分页样式 */
.fixed-pagination {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 20px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  align-items: center;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  align-items: center;
}

.preferred-teachers,
.preferred-times {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.teacher-tag {
  margin-bottom: 2px;
}

.approved-icon {
  color: #67c23a;
  margin-right: 4px;
  font-weight: bold;
}

.more-indicator {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 时间范围选择器样式 */
.time-range-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.quick-time-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-time-buttons .el-button {
  padding: 5px 12px;
  font-size: 12px;
}

/* 搜索卡片样式优化 */
.search-card {
  margin-bottom: 20px;
}

.search-card .el-form {
  padding: 10px 0;
}

.search-card .el-form-item {
  margin-bottom: 18px;
}

.search-card .el-row {
  margin-bottom: 0;
}

.search-card .el-row:not(:last-child) {
  margin-bottom: 10px;
}

/* 试听课时间样式 */
.trial-time-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.trial-date {
  font-weight: 500;
  color: #303133;
  font-size: 13px;
}

.trial-time {
  color: #606266;
  font-size: 12px;
}

.no-data {
  color: #C0C4CC;
}

/* 表格工具栏样式 */
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #EBEEF5;
  margin-bottom: 16px;
}

.toolbar-left .toolbar-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}
</style>
